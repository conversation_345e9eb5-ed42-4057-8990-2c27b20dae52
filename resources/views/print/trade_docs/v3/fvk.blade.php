<?php
$issuer = $document->issuer_address;
$seller = $document->options['different_seller'] ? $document->seller_address : $document->issuer_address;
$buyer = $document->buyer_address;
$bank = $document->bank_data;
$vat = $document->vat;
$vat_source = $document->vat_source;
$vat_final = $document->vat_final;
$options = $document->options;
$items = $document->items;
$sourcedoc = $record->getSourceDoc();
$image = tenantLogo($record->tenant);
$invoiceConfig = $record->tenant->getInvoiceConfiguration();
?>
@extends('print.trade_docs.v3.layout')
@section('content')
    <table style="width:100%;border:0;margin-left:auto;margin-right: auto;margin-bottom:3mm;">
        <tr valign='bottom'>
            <td style="width:30%;">
                @if(!empty($image))
                    <img src="{{ $image }}" style="max-width:220px;" width="auto">
                @endif
            </td>
            <td style="width:40%;text-align:center;vertical-align: bottom;">
                <div style="">
                    <h2 style='font-size: 24px;margin-bottom:2mm;'>Faktura VAT korekta</h2>
                    <div style="font-size:16px;"><b>{{ $record->full_doc_number ?? '' }} </b></div>
                    @if($document->getOption('reverse_charge', false))
                        <div style="font-size:14px;margin-top:3mm;"><b>Odwrotne obciążenie VAT </b></div>
                    @endif
                    @if($document->getOption('mpp', false))
                        <div style="font-size:10px;margin-top:3mm;">
                            <b>{{__('app.trade_docs._.mpp')}}</b></div>
                    @endif
                </div>
            </td>
            <td style="width:30%;" class="data">Data faktury: {{ $record->issued_at->format('Y-m-d') }}<br>
                Data sprzedaży: {{ $record->sells_date->format('Y-m-d') }}</td>
        </tr>
        <tr>
            <td colspan="3" style="width:40%;text-align:center;font-size:14px;padding-top: 4mm">
                <div>
                    <strong>Dokument korygowany</strong>
                </div>
                <div style="font-size:10pt;margin-top: 3mm;">
                Nr: <strong>{{$sourcedoc->full_doc_number}}</strong>, data wystawienia: {{$sourcedoc->issued_at->format('Y-m-d')}}, data sprzedaży: {{$sourcedoc->sells_date->format('Y-m-d')}}, waluta: {{$sourcedoc->currency}}<br>
                </div>
            </td>
        </tr>
        @if ($record->is_final_invoice ?? false)
            <tr>
                <td style="width:30%;">&nbsp;</td>
                <td style="width:40%;text-align:center;font-size:14px;padding-top:2mm;">
                    <div style=""><b>Faktura końcowa</b></div>
                </td>
                <td style="width:30%;">&nbsp;</td>
            </tr>
        @endif
        @if ($options['duplicate'] ?? false)
            <tr>
                <td style="width:30%;">&nbsp;</td>
                <td style="width:40%;text-align:center;font-size:14px;">
                    <div style=""><b>Duplikat, <small>data wystawienia: {{$options['duplicate']}}</small></b></div>
                </td>
                <td style="width:30%;">&nbsp;</td>
            </tr>
        @endif
        @if (filled($invoiceConfig->get('special_note')) && in_array('special_note', $invoiceConfig->get('templates')['v3']['selected_fields'] ?? []))
            <tr>
                <td colspan="3" style="text-align:center;font-size:14px; padding-top: 3mm;">
                    <div style="border-top: 1px solid black;border-bottom: 1px solid black;">
                        <b>{{$invoiceConfig->get('special_note')}}</b></div>
                </td>
            </tr>
        @endif
        <tr>
            <td style="width:30%;">&nbsp;</td>
            <td style="width:40%;text-align:center;font-size:12px;"></td>
            <td style="width:30%;text-align:right;font-size:12px;"></td>
        </tr>
    </table>
    <table style="vertical-align: top; width:100%;border:0;margin-left:auto;margin-right: auto;margin-bottom:0.5cm;">
        <tr>
            <td width="50%"  valign="top">
                <table class="dane" valign="top">
                    <tr>
                        <td colspan="2"><strong>SPRZEDAWCA:</strong></td>
                        <td>&nbsp;</td>
                    </tr>
                    <tr>
                        <td>NAZWA:</td>
                        <td>{!! nl2br($seller['name']) !!}</td>
                    </tr>
                    <tr>
                        <td>ADRES:</td>
                        <td>{{$seller['address']}}, {{$seller['postcode']}} {{$seller['city']}}</td>
                    </tr>
                    @if ($seller['vat_id'])
                        <tr>
                            <td>NIP:</td>
                            <td>{{$seller['vat_id']}}</td>
                        </tr>
                    @endif
                    <tr>
                        <td>BANK:</td>
                        <td>{{$bank['bank_name'] ?? 'brak'}}</td>
                    </tr>
                    @if($bank['bank_swift'] ?? false)
                        <tr>
                            <td>BANK SWIFT:</td>
                            <td>{{$bank['bank_swift']}}</td>
                        </tr>
                    @endif
                    <tr>
                        <td>NR KONTA:</td>
                        <td>{{$bank['bank_account'] ?? 'brak'}}</td>
                    </tr>
                </table>
            </td>
            <td width="50%"  valign="top">
                <table class="dane" valign="top">
                    <tr>
                        <td><strong>NABYWCA:</strong></td>
                        <td>&nbsp;</td>
                    </tr>
                    <tr>
                        <td>NAZWA:</td>
                        <td>{!! nl2br($buyer['name']) !!}</td>
                    </tr>
                    <tr>
                        <td>ADRES:</td>
                        <td>{{$buyer['postcode']}} {{$buyer['city']}}, {{$buyer['address']}}</td>
                    </tr>
                    <tr>
                        <td style="padding-bottom:0;">NIP</td>
                        <td style="padding-bottom:0;">{{$buyer['vat_id'] ?? ''}}</td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>

    @include('print.trade_docs.v3.parts.fvk_items')
    @include('print.trade_docs.v3.parts.fvk_summary')

    <br>
    <table class="tresctabeli" style="width:19cm;margin-left:auto;margin-right: auto;" align="center" cellpadding="2"
           cellspacing="0">
        <tr>
            <td valign="top" style="width:11cm;">
            </td>
        </tr>
    </table>
    <br>
    @if(!empty($document->getNote()))
        <table class="tresctabeli" style="width:19cm;margin-left:auto;margin-right: auto;" cellpadding="0" cellspacing="0">
            <tr>
                <td style="text-align: left;"><b>Uwagi:</b></td>
            </tr>
            <tr>
                <td style="text-align: left;">{{$document->getNote()}}</td>
            </tr>
        </table>
    @endif
@endsection
