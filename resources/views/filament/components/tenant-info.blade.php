<div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 space-y-2">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
            <h4 class="font-semibold text-gray-900 dark:text-gray-100">{{ $tenant->name }}</h4>
            @if($tenant->address)
                <p class="text-sm text-gray-600 dark:text-gray-400">{{ $tenant->address }}</p>
            @endif
            @if($tenant->city || $tenant->postcode)
                <p class="text-sm text-gray-600 dark:text-gray-400">
                    {{ $tenant->postcode }} {{ $tenant->city }}
                </p>
            @endif
        </div>
        
        <div class="space-y-1">
            @if($tenant->email)
                <p class="text-sm text-gray-600 dark:text-gray-400">
                    <span class="font-medium">Email:</span> {{ $tenant->email }}
                </p>
            @endif
            @if($tenant->phone)
                <p class="text-sm text-gray-600 dark:text-gray-400">
                    <span class="font-medium">Phone:</span> {{ $tenant->phone }}
                </p>
            @endif
            @if($tenant->vat_id)
                <p class="text-sm text-gray-600 dark:text-gray-400">
                    <span class="font-medium">VAT ID:</span> {{ $tenant->vat_id }}
                </p>
            @endif
            <p class="text-sm">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $tenant->is_active ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100' }}">
                    {{ $tenant->is_active ? 'Active' : 'Inactive' }}
                </span>
            </p>
        </div>
    </div>
</div>
