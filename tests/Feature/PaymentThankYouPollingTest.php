<?php

namespace Tests\Feature;

use App\Enums\PaymentStatus;
use App\Filament\App\Pages\PaymentThankYou;
use App\Models\Payment;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Livewire\Livewire;
use Tests\TestCase;

class PaymentThankYouPollingTest extends TestCase
{
    use DatabaseTransactions;

    protected User $user;
    protected Tenant $tenant;
    protected Plan $plan;
    protected Subscription $subscription;
    protected Payment $payment;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->tenant = Tenant::factory()->create();
        $this->plan = Plan::factory()->create();
        $this->subscription = Subscription::factory()
            ->forUserAndTenant($this->user, $this->tenant)
            ->withPlan($this->plan)
            ->create();
    }

    public function test_polling_starts_for_pending_payment()
    {
        $this->payment = Payment::factory()->pending()->create([
            'user_id' => $this->user->id,
            'tenant_id' => $this->tenant->id,
            'subscription_id' => $this->subscription->id,
        ]);

        $this->actingAs($this->user);

        $component = Livewire::test(PaymentThankYou::class, ['paymentId' => $this->payment->hash])
            ->assertSet('paymentFound', true)
            ->assertSet('isPolling', true)
            ->assertSet('hasPollingError', false);

        $this->assertTrue($component->get('isPolling'));
    }

    public function test_polling_does_not_start_for_completed_payment()
    {
        $this->payment = Payment::factory()->completed()->create([
            'user_id' => $this->user->id,
            'tenant_id' => $this->tenant->id,
            'subscription_id' => $this->subscription->id,
        ]);

        $this->actingAs($this->user);

        $component = Livewire::test(PaymentThankYou::class, ['paymentId' => $this->payment->hash])
            ->assertSet('paymentFound', true)
            ->assertSet('isPolling', false);

        $this->assertFalse($component->get('isPolling'));
    }

    public function test_check_payment_status_updates_payment()
    {
        $this->payment = Payment::factory()->pending()->create([
            'user_id' => $this->user->id,
            'tenant_id' => $this->tenant->id,
            'subscription_id' => $this->subscription->id,
        ]);

        $this->actingAs($this->user);

        $component = Livewire::test(PaymentThankYou::class, ['paymentId' => $this->payment->hash])
            ->assertSet('isPolling', true);

        // Simulate payment status change in database
        $this->payment->update(['status' => PaymentStatus::COMPLETED, 'paid_at' => now()]);

        // Call the polling method
        $component->call('checkPaymentStatus')
            ->assertSet('isPolling', false);

        // Verify the component has the updated payment
        $this->assertEquals(PaymentStatus::COMPLETED, $component->get('payment')->status);
    }

    public function test_polling_stops_after_max_attempts()
    {
        $this->payment = Payment::factory()->pending()->create([
            'user_id' => $this->user->id,
            'tenant_id' => $this->tenant->id,
            'subscription_id' => $this->subscription->id,
        ]);

        $this->actingAs($this->user);

        $component = Livewire::test(PaymentThankYou::class, ['paymentId' => $this->payment->hash])
            ->set('pollingAttempts', 60) // Set to max attempts
            ->call('checkPaymentStatus')
            ->assertSet('isPolling', false)
            ->assertSet('hasPollingError', true);

        $this->assertStringContainsString('Przekroczono maksymalny czas', $component->get('pollingErrorMessage'));
    }

    public function test_refresh_payment_status_resets_polling()
    {
        $this->payment = Payment::factory()->pending()->create([
            'user_id' => $this->user->id,
            'tenant_id' => $this->tenant->id,
            'subscription_id' => $this->subscription->id,
        ]);

        $this->actingAs($this->user);

        $component = Livewire::test(PaymentThankYou::class, ['paymentId' => $this->payment->hash])
            ->set('hasPollingError', true)
            ->set('pollingErrorMessage', 'Test error')
            ->set('pollingAttempts', 10)
            ->call('refreshPaymentStatus')
            ->assertSet('hasPollingError', false)
            ->assertSet('pollingErrorMessage', '')
            ->assertSet('pollingAttempts', 1) // Should be 1 after calling checkPaymentStatus
            ->assertSet('isPolling', true);
    }

    public function test_payment_not_found_shows_error()
    {
        $this->actingAs($this->user);

        Livewire::test(PaymentThankYou::class, ['paymentId' => 'non-existent-hash'])
            ->assertSet('paymentFound', false)
            ->assertSet('isPolling', false);
    }
}
