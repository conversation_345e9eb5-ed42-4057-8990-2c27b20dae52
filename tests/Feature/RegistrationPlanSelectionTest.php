<?php

namespace Tests\Feature;

use App\Enums\Roles;
use App\Enums\SubscriptionStatus;
use App\Enums\SystemModules;
use App\Models\Plan;
use App\Models\Registration;
use App\Models\Subscription;
use App\Models\Tenant;
use App\Models\User;
use App\Repositories\DocumentSeriesRepository;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Tests\TestCase;

class RegistrationPlanSelectionTest extends TestCase
{
    use DatabaseTransactions;

    private Registration $registration;
    private Plan $testPlan;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test plan
        $this->testPlan = Plan::factory()->standard()->create();

        // Create test registration
        $this->registration = Registration::create([
            'email' => '<EMAIL>',
            'vat_id' => '*********0',
            'registration_hash' => 'test-hash-123',
            'confirmation_code' => '123456',
            'confirmed_at' => now(),
            'data' => [
                'user' => [
                    'name' => 'Jan',
                    'surname' => '<PERSON>walski',
                    'password' => 'password123',
                    'password_confirmation' => 'password123',
                    'adress' => 'ul. Testowa 1',
                    'number' => '*********',
                ],
                'company' => [
                    'name' => 'Test Company Sp. z o.o.',
                    'postcode' => '00-001',
                    'city' => 'Warszawa',
                    'phone' => '*********',
                    'email' => '<EMAIL>',
                    'contact_name' => 'Jan Kowalski',
                    'website' => 'https://test.com',
                    'address' => 'ul. Firmowa 1, Warszawa',
                    'tax_residency_country' => 'PL',
                    'business_type' => 1,
                    'vat_type' => 1,
                    'vat_id' => '123-456-78-90',
                    'tax_type' => 1,
                    'accounting_type' => 1,
                    'meta' => [
                        'accounting' => [
                            'regon' => '*********',
                            'bdo' => 'TEST123',
                        ],
                        'bank_accounts' => [
                            [
                                'account_name' => 'Konto główne',
                                'bank_name' => 'Test Bank',
                                'bank_account' => '12 3456 7890 1234 5678 9012 3456',
                                'bank_swift' => 'TESTPL22',
                                'bank_iban' => null,
                                'bank_currency' => 'PLN',
                            ]
                        ]
                    ]
                ]
            ]
        ]);

        Session::put('registration_code', $this->registration->confirmation_code);
    }

    public function test_registration_completion_with_plan_selection()
    {
        // Prepare form data with plan selection
        $formData = array_merge($this->registration->data, [
            'selected_plan_id' => $this->testPlan->id
        ]);

        // Mock the ConfirmRegistrationData page behavior
        $this->simulateRegistrationCompletion($formData);

        // Verify tenant was created with correct configuration
        $tenant = Tenant::where('email', '<EMAIL>')->first();
        $this->assertNotNull($tenant);
        $this->assertEquals($this->testPlan->features, $tenant->config['modules']);
        $this->assertEquals($this->testPlan->id, $tenant->config['selected_plan_id']);

        // Verify user was created
        $user = User::where('email', $this->registration->email)->first();
        $this->assertNotNull($user);
        $this->assertTrue($user->hasRole(Roles::TENANT_ADMIN->value));

        // Verify subscription was created
        $subscription = Subscription::where('user_id', $user->id)
            ->where('tenant_id', $tenant->id)
            ->where('plan_id', $this->testPlan->id)
            ->first();

        $this->assertNotNull($subscription);
        $this->assertEquals(SubscriptionStatus::ACTIVE, $subscription->status);
        $this->assertEquals($this->testPlan->price, $subscription->price);

        // Verify tenant metadata contains subscription info
        $this->assertNotNull($tenant->meta);
        $meta = $tenant->meta->meta;
        $this->assertArrayHasKey('subscription', $meta);
        $this->assertEquals($this->testPlan->id, $meta['subscription']['plan_id']);
        $this->assertEquals($this->testPlan->name, $meta['subscription']['plan_name']);
    }

    public function test_registration_fails_with_invalid_plan()
    {
        // Create inactive plan
        $inactivePlan = Plan::factory()->inactive()->create();

        $formData = array_merge($this->registration->data, [
            'selected_plan_id' => $inactivePlan->id
        ]);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Invalid plan selected');

        $this->simulateRegistrationCompletion($formData);
    }

    public function test_registration_fails_with_nonexistent_plan()
    {
        $formData = array_merge($this->registration->data, [
            'selected_plan_id' => 99999 // Non-existent plan ID
        ]);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Invalid plan selected');

        $this->simulateRegistrationCompletion($formData);
    }

    public function test_tenant_modules_are_set_from_selected_plan()
    {
        // Create plan with specific features
        $customPlan = Plan::factory()->create([
            'features' => [
                SystemModules::INVOICES->value,
                SystemModules::SIMPLE_PRODUCTS->value,
                SystemModules::LOGO->value,
            ]
        ]);

        $formData = array_merge($this->registration->data, [
            'selected_plan_id' => $customPlan->id
        ]);

        $this->simulateRegistrationCompletion($formData);

        $tenant = Tenant::where('email', '<EMAIL>')->first();
        $this->assertEquals($customPlan->features, $tenant->config['modules']);

        // Verify tenant has the correct modules
        $this->assertTrue($tenant->hasModule(SystemModules::INVOICES));
        $this->assertTrue($tenant->hasModule(SystemModules::SIMPLE_PRODUCTS));
        $this->assertTrue($tenant->hasModule(SystemModules::LOGO));
        $this->assertFalse($tenant->hasModule(SystemModules::WAREHOUSE));
    }

    public function test_subscription_dates_are_calculated_correctly()
    {
        // Create plan with 12-month period
        $yearlyPlan = Plan::factory()->create([
            'period' => \App\Enums\PlanPeriod::YEAR,
            'is_active' => true,
        ]);

        $formData = array_merge($this->registration->data, [
            'selected_plan_id' => $yearlyPlan->id
        ]);

        $beforeCompletion = now()->startOfDay();
        $this->simulateRegistrationCompletion($formData);
        $afterCompletion = now();

        $subscription = Subscription::where('plan_id', $yearlyPlan->id)->first();
        $this->assertNotNull($subscription, 'Subscription should be created for plan ID: ' . $yearlyPlan->id);

        // Verify start date is around now
        $this->assertTrue($subscription->starts_at >= $beforeCompletion);
        $this->assertTrue($subscription->starts_at <= $afterCompletion);

        // Verify end date is 12 months from start
        $expectedEndDate = $subscription->starts_at->copy()->addMonths(12)->endOfDay();
        $this->assertEquals(
            $expectedEndDate->format('Y-m-d'),
            $subscription->ends_at->format('Y-m-d')
        );
    }

    /**
     * Simulate the registration completion process
     */
    private function simulateRegistrationCompletion(array $formData): void
    {
        $dataCollection = collect($formData);
        $companyData = $dataCollection->get('company');
        $userData = $dataCollection->get('user');
        $selectedPlanId = $dataCollection->get('selected_plan_id');

        // Get selected plan and its features
        $selectedPlan = Plan::find($selectedPlanId);
        if (!$selectedPlan || !$selectedPlan->is_active) {
            throw new \Exception('Invalid plan selected');
        }

        $tenantConfig = [
            'modules' => $selectedPlan->features ?? [SystemModules::INVOICES->value],
            'selected_plan_id' => $selectedPlanId,
        ];

        $tenant = Tenant::create([
            ...Arr::except($companyData, 'meta'),
            'hash' => bin2hex(random_bytes(16)),
            'config' => $tenantConfig,
            'is_active' => true,
        ]);

        $tenant->meta()->create(
            Arr::only($companyData, 'meta')
        );

        // Create user
        $user = User::create([
            'name' => $this->registration->email,
            'email' => $this->registration->email,
            'password' => Hash::make($userData['password']),
            'active' => true,
        ]);

        $user->assignRole(Roles::TENANT_ADMIN);

        $user->profile()->create([
            ...Arr::except($userData, ['password', 'password_confirmation']),
            'lang' => 'pl'
        ]);

        // Associate user with tenant
        $user->tenant()->attach($tenant->id);

        // Create subscription for the selected plan
        Subscription::create([
            'user_id' => $user->id,
            'tenant_id' => $tenant->id,
            'plan_id' => $selectedPlan->id,
            'status' => SubscriptionStatus::ACTIVE,
            'price' => $selectedPlan->price,
            'starts_at' => now(),
            'ends_at' => now()->addMonths($selectedPlan->period->value)->endOfDay(),
        ]);

        // Store plan information in tenant metadata
        $existingMeta = $tenant->meta?->meta ?? [];
        $existingMeta['subscription'] = [
            'plan_id' => $selectedPlan->id,
            'plan_name' => $selectedPlan->name,
            'selected_at' => now()->toISOString(),
        ];

        if ($tenant->meta) {
            $tenant->meta->update(['meta' => $existingMeta]);
        } else {
            $tenant->meta()->create(['meta' => $existingMeta]);
        }

        DocumentSeriesRepository::seedDefaultSeries($tenant, 'trade');

        $this->registration->update(['finished_at' => now()]);
    }
}
