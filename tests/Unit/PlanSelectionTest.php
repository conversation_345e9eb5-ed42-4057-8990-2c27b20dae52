<?php

namespace Tests\Unit;

use App\Enums\SystemModules;
use App\Models\Plan;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class PlanSelectionTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();

        // Clear existing plans to ensure clean test environment
        Plan::query()->delete();
    }

    public function test_plan_options_generation()
    {
        // Create test plans
        $trialPlan = Plan::factory()->trial()->create();
        $basicPlan = Plan::factory()->basic()->create();
        $inactivePlan = Plan::factory()->inactive()->create();

        // Get active plans
        $activePlans = Plan::where('is_active', true)->get();

        // Test plan options generation logic
        $planOptions = $activePlans->mapWithKeys(function (Plan $plan) {
            $features = collect($plan->features)
                ->map(fn($featureId) => SystemModules::get_label($featureId))
                ->filter()
                ->join(', ');
            
            $priceText = $plan->price > 0 
                ? number_format($plan->price, 2, ',', ' ') . ' PLN / ' . $plan->period->value . ' miesięcy'
                : 'Bezpłatny';
            
            return [
                $plan->id => $plan->name . ' - ' . $priceText
            ];
        })->toArray();

        // Assertions
        $this->assertCount(2, $planOptions); // Only active plans
        $this->assertArrayHasKey($trialPlan->id, $planOptions);
        $this->assertArrayHasKey($basicPlan->id, $planOptions);
        $this->assertArrayNotHasKey($inactivePlan->id, $planOptions);
        
        // Check trial plan formatting
        $this->assertStringContainsString('Bezpłatny', $planOptions[$trialPlan->id]);

        // Check basic plan formatting
        $this->assertStringContainsString('19,00 PLN', $planOptions[$basicPlan->id]);
    }

    public function test_plan_descriptions_generation()
    {
        // Create test plan with features
        $plan = Plan::factory()->standard()->create();

        // Test plan descriptions generation logic
        $planDescriptions = Plan::where('is_active', true)
            ->get()
            ->mapWithKeys(function (Plan $plan) {
                $features = collect($plan->features)
                    ->map(fn($featureId) => SystemModules::get_label($featureId))
                    ->filter()
                    ->join(', ');
                
                $description = $plan->description;
                if ($features) {
                    $description .= "\n\nDostępne funkcje: " . $features;
                }
                
                return [$plan->id => $description];
            })
            ->toArray();

        // Assertions
        $this->assertArrayHasKey($plan->id, $planDescriptions);
        $this->assertStringContainsString($plan->description, $planDescriptions[$plan->id]);
        $this->assertStringContainsString('Dostępne funkcje:', $planDescriptions[$plan->id]);
        $this->assertStringContainsString('Faktury sprzedaży', $planDescriptions[$plan->id]);
    }

    public function test_system_modules_label_mapping()
    {
        $testCases = [
            SystemModules::INVOICES->value => 'Faktury sprzedaży',
            SystemModules::SIMPLE_PRODUCTS->value => 'Lista produktów',
            SystemModules::PURCHASE_INVOICES->value => 'Faktury zakupu',
            SystemModules::SIMPLE_CHARTS->value => 'Wykresy',
            SystemModules::LOGO->value => 'Logo',
            SystemModules::INVOICE_TEMPLATES->value => 'Szablony faktur',
        ];

        foreach ($testCases as $moduleId => $expectedLabel) {
            $actualLabel = SystemModules::get_label($moduleId);
            $this->assertEquals($expectedLabel, $actualLabel, 
                "SystemModules::get_label({$moduleId}) should return '{$expectedLabel}'");
        }
    }

    public function test_plan_features_filtering()
    {
        // Create plan with mixed valid and invalid features
        $plan = Plan::factory()->create([
            'features' => [
                SystemModules::INVOICES->value,
                SystemModules::SIMPLE_PRODUCTS->value,
                999, // Invalid feature ID
                null, // Null value
            ]
        ]);

        // Test feature filtering logic
        $features = collect($plan->features)
            ->map(fn($featureId) => SystemModules::get_label($featureId))
            ->filter(fn($label) => $label !== 'none' && !empty($label)) // Filter out invalid entries
            ->join(', ');

        // Assertions
        $this->assertStringContainsString('Faktury sprzedaży', $features);
        $this->assertStringContainsString('Lista produktów', $features);
        $this->assertStringNotContainsString('none', $features); // Invalid features return 'none'
    }

    public function test_price_formatting()
    {
        $testCases = [
            ['price' => 0, 'period' => 1, 'expected' => 'Bezpłatny'],
            ['price' => 19.00, 'period' => 1, 'expected' => '19,00 PLN / 1 miesięcy'],
            ['price' => 390.50, 'period' => 12, 'expected' => '390,50 PLN / 12 miesięcy'],
            ['price' => 1000, 'period' => 6, 'expected' => '1 000,00 PLN / 6 miesięcy'],
        ];

        foreach ($testCases as $testCase) {
            $priceText = $testCase['price'] > 0 
                ? number_format($testCase['price'], 2, ',', ' ') . ' PLN / ' . $testCase['period'] . ' miesięcy'
                : 'Bezpłatny';

            $this->assertEquals($testCase['expected'], $priceText,
                "Price formatting failed for price: {$testCase['price']}, period: {$testCase['period']}");
        }
    }

    public function test_only_active_plans_are_retrieved()
    {
        // Create mix of active and inactive plans
        Plan::factory()->active()->count(3)->create();
        Plan::factory()->inactive()->count(2)->create();

        $activePlans = Plan::where('is_active', true)->get();
        $allPlans = Plan::all();

        $this->assertCount(3, $activePlans);
        $this->assertCount(5, $allPlans);

        // Ensure all retrieved plans are active
        foreach ($activePlans as $plan) {
            $this->assertTrue($plan->is_active);
        }
    }
}
