<?php

namespace Tests\Unit;

use App\Enums\AccountingTypesPL;
use App\Enums\PartnerBusinessTypes;
use App\Enums\PartnerVATTypes;
use App\Enums\TaxResidencyCountries;
use App\Enums\TaxTypePL;
use App\Models\Partner;
use App\Models\Tenant;
use Tests\TestCase;

class TenantToPartnerConversionTest extends TestCase
{
    public function test_tenant_converts_to_partner_with_all_mappable_fields()
    {
        // Create a tenant with all possible data
        $tenant = new Tenant([
            'id' => 123,
            'hash' => 'test_hash_******************************12',
            'name' => 'Test Company Ltd.',
            'address' => '123 Test Street, Test District',
            'postcode' => '12-345',
            'city' => 'Test City',
            'phone' => '+**************',
            'email' => '<EMAIL>',
            'contact_name' => 'John Doe',
            'website' => 'https://testcompany.com',
            'system_domain' => 'testcompany.system.com', // This won't be mapped
            'vat_id' => '**********',
            'vat_type' => PartnerVATTypes::LOCAL,
            'tax_residency_country' => TaxResidencyCountries::PL,
            'tax_type' => TaxTypePL::PROGRESSIVE, // This won't be mapped
            'accounting_type' => AccountingTypesPL::IOR, // This won't be mapped
            'business_type' => PartnerBusinessTypes::BUSINESS,
            'is_active' => true,
            'config' => ['modules' => ['invoices'], 'test' => 'data'], // This won't be mapped
        ]);

        // Convert to partner
        $partner = $tenant->convertToPartner(123);

        // Assert it's a Partner instance
        $this->assertInstanceOf(Partner::class, $partner);

        // Assert direct mappings
        $this->assertEquals($tenant->id, $partner->installation);
        $this->assertEquals($tenant->hash, $partner->hash);
        $this->assertEquals($tenant->name, $partner->name);
        $this->assertEquals($tenant->address, $partner->address);
        $this->assertEquals($tenant->postcode, $partner->postcode);
        $this->assertEquals($tenant->city, $partner->city);
        $this->assertEquals($tenant->phone, $partner->phone);
        $this->assertEquals($tenant->email, $partner->email);
        $this->assertEquals($tenant->contact_name, $partner->contact_name);
        $this->assertEquals($tenant->website, $partner->website);
        $this->assertEquals($tenant->vat_id, $partner->vat_id);
        $this->assertEquals($tenant->vat_type, $partner->vat_type);
        $this->assertEquals($tenant->business_type, $partner->business_type);
        $this->assertEquals($tenant->tax_residency_country, $partner->tax_residency_country);
        $this->assertEquals($tenant->is_active, $partner->is_active);
        $this->assertNotNull($partner->short_name);
    }

    public function test_tenant_converts_to_partner_with_minimal_data()
    {
        // Create a tenant with minimal required data
        $tenant = new Tenant([
            'id' => 456,
            'hash' => 'minimal_hash_******************************1',
            'name' => 'Minimal Company',
            'is_active' => false,
        ]);

        // Convert to partner
        $partner = $tenant->convertToPartner(456);

        // Assert basic mappings
        $this->assertEquals($tenant->id, $partner->installation);
        $this->assertEquals($tenant->hash, $partner->hash);
        $this->assertEquals($tenant->name, $partner->name);
        $this->assertEquals($tenant->is_active, $partner->is_active);

        // Assert nullable fields are properly handled
        $this->assertNull($partner->address);
        $this->assertNull($partner->postcode);
        $this->assertNull($partner->city);
        $this->assertNull($partner->phone);
        $this->assertNull($partner->email);
        $this->assertNull($partner->contact_name);
        $this->assertNull($partner->website);
        $this->assertNull($partner->vat_id);
        $this->assertNull($partner->vat_type);
        $this->assertNull($partner->business_type);
    }

    public function test_tenant_converts_to_partner_with_enum_values()
    {
        // Test different enum combinations
        $tenant = new Tenant([
            'id' => 789,
            'hash' => 'enum_test_hash_******************************',
            'name' => 'Enum Test Company',
            'vat_type' => PartnerVATTypes::NOTVAT,
            'business_type' => PartnerBusinessTypes::INDIVIDUAL,
            'tax_residency_country' => TaxResidencyCountries::DE,
            'is_active' => true,
        ]);

        $partner = $tenant->convertToPartner(789);

        // Assert enum values are properly converted
        $this->assertEquals(PartnerVATTypes::NOTVAT, $partner->vat_type);
        $this->assertEquals(PartnerBusinessTypes::INDIVIDUAL, $partner->business_type);
        $this->assertEquals(TaxResidencyCountries::DE, $partner->tax_residency_country);
    }

    public function test_unmappable_fields_documentation()
    {
        // This test documents which fields cannot be mapped
        $tenant = new Tenant([
            'id' => 999,
            'hash' => 'doc_test_hash_******************************12',
            'name' => 'Documentation Test',
            'system_domain' => 'should_not_be_mapped.com',
            'tax_type' => TaxTypePL::LINEAR,
            'accounting_type' => AccountingTypesPL::IOR,
            'config' => ['test' => 'config'],
        ]);

        $partner = $tenant->convertToPartner(999);

        // These fields should not exist in Partner model
        $this->assertObjectNotHasProperty('system_domain', $partner);
        $this->assertObjectNotHasProperty('tax_type', $partner);
        $this->assertObjectNotHasProperty('accounting_type', $partner);
        $this->assertObjectNotHasProperty('config', $partner);
    }
}
