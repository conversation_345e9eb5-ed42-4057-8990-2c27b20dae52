<?php

namespace Tests\Unit;

use App\Models\SystemSetting;
use App\Repositories\SystemSettingsRepository;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class SystemSettingsRepositoryTest extends TestCase
{
    use DatabaseTransactions;

    private SystemSettingsRepository $repository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = new SystemSettingsRepository();
        Cache::flush(); // Clear cache before each test
    }

    public function test_can_set_and_get_setting(): void
    {
        $key = 'test_setting';
        $value = 'test_value';
        $description = 'Test setting description';

        // Set the setting
        $setting = $this->repository->set($key, $value, $description);

        $this->assertInstanceOf(SystemSetting::class, $setting);
        $this->assertEquals($key, $setting->key);
        $this->assertEquals($value, $setting->value);
        $this->assertEquals($description, $setting->description);

        // Get the setting
        $retrievedValue = $this->repository->get($key);
        $this->assertEquals($value, $retrievedValue);
    }

    public function test_get_returns_default_when_setting_not_exists(): void
    {
        $default = 'default_value';
        $value = $this->repository->get('non_existent_key', $default);

        $this->assertEquals($default, $value);
    }

    public function test_can_set_system_tenant_id(): void
    {
        $tenantId = 123;

        $setting = $this->repository->setSystemTenantId($tenantId);

        $this->assertEquals('system_tenant_id', $setting->key);
        $this->assertEquals($tenantId, $setting->value);

        $retrievedId = $this->repository->getSystemTenantId();
        $this->assertEquals($tenantId, $retrievedId);
    }

    public function test_can_forget_setting(): void
    {
        $key = 'test_setting';
        $value = 'test_value';

        // Set the setting
        $this->repository->set($key, $value);

        // Verify it exists
        $this->assertTrue($this->repository->exists($key));

        // Forget the setting
        $deleted = $this->repository->forget($key);

        $this->assertTrue($deleted);
        $this->assertFalse($this->repository->exists($key));
    }

    public function test_caching_works(): void
    {
        $key = 'cached_setting';
        $value = 'cached_value';

        // Set the setting
        $this->repository->set($key, $value);

        // First get should hit database and cache
        $firstGet = $this->repository->get($key);
        $this->assertEquals($value, $firstGet);

        // Manually delete from database but not cache
        SystemSetting::where('key', $key)->delete();

        // Second get should return cached value
        $secondGet = $this->repository->get($key);
        $this->assertEquals($value, $secondGet);
    }

    public function test_can_set_and_get_system_bank_account(): void
    {
        $bankAccountIndex = '0';

        $setting = $this->repository->setSystemBankAccount($bankAccountIndex);

        $this->assertEquals('system_bank_account', $setting->key);
        $this->assertEquals($bankAccountIndex, $setting->value);

        $retrievedIndex = $this->repository->getSystemBankAccount();
        $this->assertEquals($bankAccountIndex, $retrievedIndex);
    }

    public function test_can_set_and_get_system_fvs_document_series(): void
    {
        $seriesId = 456;

        $setting = $this->repository->setSystemFvsDocumentSeries($seriesId);

        $this->assertEquals('system_fvs_document_series', $setting->key);
        $this->assertEquals($seriesId, $setting->value);

        $retrievedId = $this->repository->getSystemFvsDocumentSeries();
        $this->assertEquals($seriesId, $retrievedId);
    }

    public function test_can_set_and_get_system_tenant_admin_user(): void
    {
        $userId = 789;

        $setting = $this->repository->setSystemTenantAdminUser($userId);

        $this->assertEquals('system_tenant_admin_user', $setting->key);
        $this->assertEquals($userId, $setting->value);

        $retrievedId = $this->repository->getSystemTenantAdminUser();
        $this->assertEquals($userId, $retrievedId);
    }
}
