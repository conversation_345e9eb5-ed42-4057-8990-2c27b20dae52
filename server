#!/usr/bin/env bash

set -o allexport
source .env set
set +o allexport

args=("$@")

dbservice=mariadb

if [ "$APP_ENV" == "production" ]; then
    export NGINX_CONF=default_ssl.conf
    export NGINX_BUILD=nginx.production
    dcfile=$(dirname $0)/server.docker-compose.yml
    echo "Running in production mode"
elif [ "$APP_ENV" == "staging" ]; then
    export NGINX_CONF=stage_ssl.conf
    export NGINX_BUILD=nginx
    dcfile=$(dirname $0)/stage.docker-compose.yml
    echo "Running in staging mode"
elif [ "$APP_ENV" == "replica" ]; then
    export NGINX_CONF=stage_ssl.conf
    export NGINX_BUILD=nginx
    dbservice=replica
    dcfile=$(dirname $0)/replica.docker-compose.yml
    echo "Running in replica mode"
else
    dcfile=$(dirname $0)/server.docker-compose.yml
    export NGINX_CONF=default.conf
    export NGINX_BUILD=nginx
    echo "Running in development mode"
fi

echo "Docker Compose File: $dcfile"
echo "Docker Compose ENV: ${APP_ENV}"

if [ $# -eq 0 ]; then
    echo "Usage: $0 [up|down|dev-up|dev-down|restart|start|build|shell|rootshell|logs|exec|artisan|queue|backup-db|update|build-assets]"
    echo ""
    echo "Available commands:"
    echo "  up             - Start all containers in detached mode"
    echo "  down           - Stop and remove all containers"
    echo "  dev-up         - Start containers with development profile (only in local environment)"
    echo "  dev-down       - Stop and remove containers with development profile"
    echo "  restart        - Restart containers"
    echo "  start          - Start stopped containers"
    echo "  build          - Build container images"
    echo "  shell          - Open bash shell in specified container with application user"
    echo "  rootshell      - Open bash shell in specified container with root user"
    echo "  logs           - View and follow container logs"
    echo "  exec           - Execute a command in a running container"
    echo "  artisan        - Run Laravel Artisan commands"
    echo "  queue          - Run Laravel queue commands"
    echo "  backup-db      - Backup the database and create a compressed archive"
    echo "  update         - Update the application (git pull, composer install, migrations, cache) and re-build images (optional - id build argument is provided)"
    echo "  build-assets   - Build frontend assets with npm"
    exit 1
fi

case $1 in
    # Start all containers in detached mode
    up)
        docker compose -f $dcfile up -d $2
        ;;
            # Stop and remove all containers
    down)
        docker compose -f $dcfile down $2
        ;;
            # Start containers with development profile (only in local environment)
    dev-up)
        if [ "$APP_ENV" != "local" ]; then
            echo "Running in production is not allowed"
            exit 1
        fi
        docker compose -f $dcfile --profile development up -d $2
        ;;
            # Stop and remove containers with development profile
    dev-down)
        docker compose -f $dcfile --profile development down $2
        ;;
            # Restart containers
    restart)
        docker compose -f $dcfile restart $2
        ;;
            # Backup the database and create a compressed archive
    backup-db)
        echo "Backing up database"
        echo "Clean up previous backup database"
        docker compose -f $dcfile exec $dbservice rm -rf /backup/
        echo "Change ownership of backup directory"
        docker compose -f $dcfile exec $dbservice chown -R mysql:mysql /backup
        docker compose -f $dcfile exec $dbservice chmod 777 /backup
        echo "Starting backup container"
        docker compose -f $dcfile exec -it $dbservice /usr/bin/bash /scripts/do-backup.sh
        if [ -f "docker/mysql/backup/mariadb_backup_info" ]; then
            echo "Backup file found. Compressing..."
            tar -czf storage/db_backup_"$(date +%Y%m%d%H%M%S)".tar.gz docker/mysql/backup
            echo "Compressing completed"
        fi
        echo "Backup completed"

        ;;
            # Start stopped containers
    start)
        docker compose -f $dcfile start $2
        ;;
            # Build container images
    build)
        docker compose -f $dcfile build "${args[@]:1}"
        ;;
            # Open bash shell in specified container with application user
    shell)
        docker compose -f $dcfile exec -it -u "$WWWUSER":"$WWWGROUP" $2 /bin/bash
        ;;
            # Open bash shell in specified container with root user
    rootshell)
        docker compose -f $dcfile exec -it -u root $2 /bin/bash
        ;;
            # View and follow container logs
    logs)
        docker compose -f $dcfile logs -f $2
        ;;
            # Execute a command in a running container
    exec)
        docker compose -f $dcfile exec -it -u "$WWWUSER":"$WWWGROUP" $2 "${args[@]:2}"
        ;;
            # Run Laravel Artisan commands
    artisan)
        docker compose -f $dcfile exec -it -u "$WWWUSER":"$WWWGROUP" "$ARTISAN_SERVICE" php artisan "${args[@]:1}"
        ;;
            # Update the application (git pull, composer install, migrations, cache)
    update)
        if [ "$APP_ENV" != "replica" ]; then
        git pull
        if [ "$2" = "build" ]; then
            docker compose -f $dcfile build --no-cache app
        fi
        docker compose -f $dcfile exec -it -u "$WWWUSER":"$WWWGROUP" "$ARTISAN_SERVICE" composer install --no-dev
        docker compose -f $dcfile exec -it -u "$WWWUSER":"$WWWGROUP" "$ARTISAN_SERVICE" php artisan migrate --step
        docker compose -f $dcfile exec -it -u "$WWWUSER":"$WWWGROUP" "$ARTISAN_SERVICE" php artisan event:clear
        docker compose -f $dcfile exec -it -u "$WWWUSER":"$WWWGROUP" "$ARTISAN_SERVICE" php artisan event:cache
        docker compose -f $dcfile exec -it -u "$WWWUSER":"$WWWGROUP" "$ARTISAN_SERVICE" php artisan view:clear
        docker compose -f $dcfile exec -it -u "$WWWUSER":"$WWWGROUP" "$ARTISAN_SERVICE" php artisan view:cache
        docker compose -f $dcfile exec -it -u "$WWWUSER":"$WWWGROUP" "$ARTISAN_SERVICE" php artisan config:clear
        docker compose -f $dcfile exec -it -u "$WWWUSER":"$WWWGROUP" "$ARTISAN_SERVICE" php artisan config:cache
        docker compose -f $dcfile exec -it -u "$WWWUSER":"$WWWGROUP" "$ARTISAN_SERVICE" php artisan filament:optimize
        docker compose -f $dcfile up -d
        docker compose -f $dcfile exec -it -u "$WWWUSER":"$WWWGROUP" "$ARTISAN_SERVICE" php artisan queue:restart
        echo "Update completed"
        echo "Remember to restart queue workers!!! (./server queue restart)"
        else
            echo "Update is not allowed in replica mode"
        fi
        ;;
            # Run Laravel queue commands
    queue)
        if [ $# -gt 1 ]; then
            params=":${args[*]:1}"
        else
            params=""
        fi
        docker compose -f $dcfile exec -it -u "$WWWUSER":"$WWWGROUP" "$QUEUE_SERVICE" php artisan queue$params
        ;;
            # Build frontend assets with npm
    build-assets)
        docker compose -f $dcfile exec -it -u "$WWWUSER":"$WWWGROUP" "$ARTISAN_SERVICE" npm run build
        ;;
    *)
        echo "Unknown command: $1"
        echo "Usage: $0 [up|down|dev-up|dev-down|restart|start|build|shell|rootshell|logs|exec|artisan|queue|backup-db|update|build-assets]"
        echo "Run without arguments to see detailed help"
        exit 1
        ;;
esac
