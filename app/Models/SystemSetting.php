<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Class SystemSetting
 *
 * @property int $id Primary key, auto-incremented
 * @property string $key Setting identifier key
 * @property mixed $value Setting value (JSON decoded)
 * @property string|null $description Human-readable description of the setting
 * @property string|null $created_at Timestamp of creation
 * @property string|null $updated_at Timestamp of last update
 */
class SystemSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'description',
    ];

    protected $casts = [
        'value' => 'json',
    ];

    /**
     * Get a setting by key
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function getValue(string $key, $default = null)
    {
        $setting = static::where('key', $key)->first();
        return $setting ? $setting->value : $default;
    }

    /**
     * Set a setting value
     *
     * @param string $key
     * @param mixed $value
     * @param string|null $description
     * @return SystemSetting
     */
    public static function setValue(string $key, $value, ?string $description = null): SystemSetting
    {
        return static::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'description' => $description,
            ]
        );
    }

    /**
     * Remove a setting
     *
     * @param string $key
     * @return bool
     */
    public static function removeSetting(string $key): bool
    {
        return static::where('key', $key)->delete() > 0;
    }

    /**
     * Check if a setting exists
     *
     * @param string $key
     * @return bool
     */
    public static function exists(string $key): bool
    {
        return static::where('key', $key)->exists();
    }
}
