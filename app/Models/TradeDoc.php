<?php

namespace App\Models;

use App\Enums\DocumentTypes;
use App\Enums\MoneyVOCast;
use App\Enums\PaymentTypes;
use App\Enums\TradeDocVatMethod;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property string $uuid Unique identifier of the trade document.
 * @property string|null $source_id Identifier of the source related to the trade document.
 * @property int $installation ID of the installation.
 * @property int $issuer_id ID of the issuer.
 * @property int|null $seller_id ID of the seller (optional).
 * @property int $buyer_id ID of the buyer.
 * @property string|DocumentTypes $type Type of the document.
 * @property int $document_series_id ID of the document series.
 * @property string $full_doc_number Full document number.
 * @property int $doc_number Document number (numeric part).
 * @property string $transaction_id Unique transaction identifier.
 * @property int $payment_type Type of the payment (tinyint).
 * @property string|null $payment_type_label Label for the payment type (optional).
 * @property int $payment_credit_days Number of credit days for payment.
 * @property string|\DateTimeInterface $payment_due_date Due date for the payment (YYYY-MM-DD format).
 * @property string|null|\DateTimeInterface $payment_date Date of the payment (optional).
 * @property bool $is_paid Indicates if the document is paid.
 * @property string $currency Currency code (default: PLN).
 * @property float $exchange_rate Exchange rate for the currency.
 * @property string|null $currency_rate_date Date of the exchange rate (optional).
 * @property int $vat_method VAT calculation method.
 * @property int|null $net Net amount (optional).
 * @property int|null $vat_amount VAT amount (optional).
 * @property int $gross Gross amount.
 * @property string|\DateTimeInterface $issued_at Date the document was issued (YYYY-MM-DD format).
 * @property string|\DateTimeInterface $sells_date Date of the sale (YYYY-MM-DD format).
 * @property string|null $ksef_ref KSeF reference (optional).
 * @property int $ksef_status Status of KSeF integration (default: 0).
 * @property string|null $ksef_inv_number KSeF invoice number (optional).
 * @property string|null $notes Additional notes (optional).
 * @property int $status Status of the document (tinyint).
 * @property string $final_invoice_trade_doc_id final invoice ID (uuid).
 * @property bool $is_final_invoice is final invoice.
 * @property bool $is_accepted Indicates if the document is accepted.
 * @property bool $has_correction Indicates if the document has corrections.
 * @property bool $is_cancelled Indicates if the document is cancelled.
 * @property string|null|\DateTimeInterface $cancelled_at Date and time when the document was cancelled (optional).
 * @property int|null $cancelled_by ID of the user who cancelled the document (optional).
 * @property int $creator_id ID of the user who created the document.
 * @property string|null $created_at Timestamp when the document was created (optional).
 * @property string|null $updated_at Timestamp when the document was last updated (optional).
 * @property string|null $deleted_at Timestamp when the document was deleted (optional).
 */
class TradeDoc extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    public $incrementing = false;
    public $keyType = 'string';
    public $primaryKey = 'uuid';

    protected ?DTOTradeDocMeta $metadata = null;

    protected $casts = [
        'issued_at' => 'date',
        'sells_date' => 'date',
        'cancelled_at' => 'date',
        'payment_due_date' => 'date',
        'payment_date' => 'date',
        'payment_type' => PaymentTypes::class,
        'is_paid' => 'bool',
        'is_cancelled' => 'bool',
        'is_accepted' => 'bool',
        'has_correction' => 'bool',
        'vat_method' => TradeDocVatMethod::class,
        'type' => DocumentTypes::class,
        'net' => MoneyVOCast::class,
        'vat_amount' => MoneyVOCast::class,
        'gross' => MoneyVOCast::class,
    ];

    protected $guarded = [
        'uuid',
        'installation',
        'issuer_id',
    ];

    protected static function booted()
    {
        parent::booted();
        if (auth()->check()) {
            if (auth()->user()?->isSuperAdmin() !== true) {
                static::addGlobalScope(new \App\Scopes\Installation());
            }
        }
    }

//    public function net(): Attribute
//    {
//        return Attribute::make(
//            get: fn ($state) => $state / $this->exchange_rate / 100
//        );
//    }

    public function netValue($baseCurrency = false): float
    {
        return match ($baseCurrency) {
            false => $this->net,
            default => $this->net / $this->exchange_rate,
        };
    }

    public function grossValue($baseCurrency = false): float
    {
        return match ($baseCurrency) {
            false => $this->gross,
            default => $this->gross / $this->exchange_rate,
        };
    }

    public function vatValue($baseCurrency = false): float
    {
        return match ($baseCurrency) {
            false => $this->vat_amount,
            default => $this->vat_amount / $this->exchange_rate,
        };
    }

    public function issuer(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'issuer_id');
    }

    public function seller(): BelongsTo
    {
        return $this->belongsTo(Partner::class, 'seller_id');
    }

    public function buyer(): BelongsTo
    {
        return $this->belongsTo(Partner::class, 'buyer_id');
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'creator_id');
    }


    public function items(): HasMany
    {
        return $this->hasMany(TradeDocItem::class);
    }

    public function meta(): HasOne
    {
        return $this->hasOne(TradeDocMeta::class);
    }

    public function getMeta(): DTOTradeDocMeta
    {
        if ($this->metadata === null) {
            $this->metadata = DTOTradeDocMeta::make($this->meta);
        }
        return $this->metadata;
    }

    public function addDuplicate($date)
    {
        $meta = $this->meta->meta ?? [];
        $duplicates = $meta['duplicates'] ?? [];
        $duplicates[] = $date;
        $meta['duplicates'] = $duplicates;
        $this->meta->meta = $meta;
        $this->meta->save();
    }


    public function installation(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'installation', 'id');
    }

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'installation', 'id');
    }

    public function documentSeries(): BelongsTo
    {
        return $this->belongsTo(DocumentSeriesPattern::class, 'document_series_id', 'id');
    }

    public function isCancelled(): bool
    {
        return $this->is_cancelled;
    }

    public function isNetBaseVat(): bool
    {
        return $this->vat_method === TradeDocVatMethod::BASE_ON_NET;
    }

    public function isGrossBaseVat(): bool
    {
        return $this->vat_method === TradeDocVatMethod::BASE_ON_GROSS;
    }

    public function prepaidInvoices(): hasMany
    {
        return $this->hasMany(
            related: self::class,
            foreignKey: 'final_invoice_trade_doc_id',
            localKey: 'uuid'
        );
    }

    public function finalInvoice(): belongsTo
    {
        return $this->belongsTo(self::class, 'final_invoice_trade_doc_id', 'uuid');
    }

    public function getSourceDoc(): ?TradeDoc
    {
        return $this->source_id ? self::find($this->source_id) : null;
    }

    public function correctedDocument(): HasOne
    {
        return $this->hasOne(TradeDoc::class, 'uuid', 'source_id');
    }


    public function correctionDocument(): HasOne
    {
        return $this->hasOne(TradeDoc::class, 'source_id', 'uuid');
    }

    public function isAccepted(): bool
    {
        return $this->is_accepted;
    }

    public function hasCorrection(): bool
    {
        return $this->has_correction;
    }

    public function hasFinalInvoice(): bool
    {
        return $this->final_invoice_trade_doc_id !== null;
    }
}
