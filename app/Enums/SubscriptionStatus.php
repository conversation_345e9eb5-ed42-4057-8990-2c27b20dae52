<?php

namespace App\Enums;

enum SubscriptionStatus
{
    use EnumHelper;

    case TRIAL;
    /** @private NEW nowa subskrypcja, która nie została jeszcze opłacona */
    case NEW;
    /** @private ACTIVE aktywna subskrypcja, która została opłacona */
    case ACTIVE;
    /** @private PENDING subskrypcja, która została opłacona, ale nie została jeszcze aktywowana */
    case PENDING;
    /** @private CANCELED anulowana subskrypcja */
    case CANCELED;
    /** @private EXPIRED wygasła subskrypcja */
    case EXPIRED;

    public function label()
    {
        return __('app.enums.subscription_statuses.' . $this->name);
    }
}
