<?php

namespace App\Mail;

use App\Filament\App\Pages\SubscriptionManagement;
use App\Models\Subscription;
use Filament\Facades\Filament;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class SubscriptionCanceled extends Mailable
{
    use Queueable, SerializesModels;

    public string $subManagementUrl;

    /**
     * Create a new message instance.
     */
    public function __construct(public Subscription $subscription)
    {
        $this->subManagementUrl = SubscriptionManagement::getUrl();
        $this->onQueue('emails');
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Twoja subskrypcja została anulowana!',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'email.subscription-canceled',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
