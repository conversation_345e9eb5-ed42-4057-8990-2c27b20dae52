<?php

namespace App\Mail;

use App\Models\Payment;
use App\Repositories\TenantRepository;
use App\Repositories\TradeDocsRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;

class PaymentSuccess extends Mailable
{
    use Queueable, SerializesModels;
    public string $invoiceNumber = '';

    /**
     * Create a new message instance.
     */
    public function __construct(public Payment $payment)
    {
        $this->onQueue('emails');
        if ($this->payment->subscription->invoice_id) {
            $this->invoiceNumber = $this->payment->subscription->invoice->full_doc_number;
        }
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Potwierdzen<PERSON> płatności - TwojeFaktury.eu',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'email.payment-success',
            with: [
                'amount' => number_format($this->payment->amount / 100, 2, ',', ' '),
                'transactionId' => $this->payment->hash,
                'date' => $this->payment->paid_at->format('Y-m-d H:i:s'),
                'invoiceId' => $this->invoiceNumber,
                'subscriptionId' => $this->payment->subscription->getOrderId(),
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        if (empty($this->payment->subscription->invoice_id)) {
            return [];
        }
        return [
            Attachment::fromData(
                fn() => TradeDocsRepository::getPDFData(
                    $this->payment->subscription->invoice,
                    TenantRepository::getTenantTradeDocVariant($this->payment->subscription->invoice->tenant)
                )->output(),
                name: 'Faktura_' . Str::of($this->invoiceNumber)->replace('/', '_')->title()->snake() . '.pdf'
            )
            ->withMime('application/pdf')
        ];
    }
}
