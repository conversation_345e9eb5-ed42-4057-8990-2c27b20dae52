<?php

namespace App\Listeners;

use Filament\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class PaymentCanceledListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(object $event): void
    {
        Notification::make()
            ->title('Płatność została anulowana!')
            ->body('Wybierz nową subskrypcję aby kontynuować korzystanie z naszych usług.')
            ->danger()
            ->sendToDatabase($event->payment->user);

        Mail::to($event->payment->user->email)
            ->queue(new \App\Mail\PaymentCanceled($event->payment));
    }
}
