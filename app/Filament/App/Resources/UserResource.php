<?php

namespace App\Filament\App\Resources;

use App\Enums\Roles;
use App\Enums\SystemModules;
use App\Filament\App\Resources\UserResource\Pages;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\IconPosition;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    public static function getNavigationLabel(): string
    {
        return __('app.users.navigation.label');
    }

    public static function getBreadcrumb(): string
    {
        return __('app.users.navigation.breadcrumb');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('app._.settings');
    }

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->isTenantAdmin() && tenant()->hasModule(SystemModules::EMPLOYEES);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Dane podstawowe')
                    ->columns(2)
                    ->schema([
                        Forms\Components\TextInput::make('email')
                            ->label(__('app._.email'))
                            ->email()
                            ->required()
                            ->unique(fn($record) => $record)
                            ->maxLength(255),
                        Forms\Components\Select::make('roles')
                            ->required()
                            ->relationship(
                                'roles',
                                'name',
                                fn($query) => $query->where('name', '!=', 'Super Admin')
                            )
                            ->getOptionLabelFromRecordUsing(
                                fn(Model $record) => (Roles::tryFrom($record->id)?->label() ?? 'unknown')
                            )
                            ->label(__('app._.role')),
                        Forms\Components\TextInput::make('password')
                            ->label(__('app._.password'))
                            ->password()
                            ->minLength(6)
                            ->confirmed()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('password_confirmation')
                            ->label(__('app._.password_confirmation'))
                            ->password()
                            ->requiredWith('password')
                            ->maxLength(255),
                        Forms\Components\Toggle::make('active')
                            ->columnSpanFull()
                            ->label(__('app._.is_active'))
                            ->onIcon('heroicon-m-user')
                            ->offIcon('heroicon-m-power'),
                    ]),
                Pages\UserProfileForm::getUserProfileForm()
            ]);
    }

    public static function table(Table $table): Table
    {
        $h = $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->formatStateUsing(fn(Model|User $record) => $record->fullName())
                    ->label(__('app._.name'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->label(__('app._.email'))
                    ->icon(function (Model $record) {
                        if (!$record->active) {
                            return 'heroicon-o-no-symbol';
                        }
                        return false;
                    })
                    ->iconColor('danger')
                    ->iconPosition(IconPosition::After)
                    ->tooltip(fn(Model $record) => !$record->active ? 'Nieaktywny' : false)
                    ->searchable(),
                Tables\Columns\TextColumn::make('roles.name')
                    ->label(__('app._.role'))
                    ->formatStateUsing(function (Model $record) {
                        $labels = [];
                        foreach ($record->roles as $role) {
                            $labels[] = (Roles::tryFrom($role->id)?->label() ?? 'unknown');
                        }
                        return implode(', ', $labels);
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('app._.created_at'))
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('app._.updated_at'))
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('role')
                    ->label(__('app._.role'))
                    ->relationship(
                        'roles',
                        'name',
                        fn($query) => $query->where('name', '!=', 'Super Admin')
                    )
                    ->getOptionLabelFromRecordUsing(
                        fn(Model $record) => (Roles::tryFrom($record->id)?->label() ?? 'unknown')
                    )
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->url(
                        fn(Model $record): string => self::getUrl('edit', ['record' => $record->hash])
                    ),
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
            ]);
        return $h;
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }
}
