<?php

namespace App\Filament\App\Pages;

use App\Enums\AccountingTypesPL;
use App\Enums\PartnerBusinessTypes;
use App\Enums\PartnerVATTypes;
use App\Enums\SystemModules;
use App\Enums\TaxResidencyCountries;
use App\Enums\TaxTypePL;
use App\Models\Company;
use App\Models\DTOTenantMetadata;
use App\Models\DTOAccountingData;
use App\Models\DTOBankData;
use App\Models\DTOImageData;
use App\Models\Tenant;
use App\Repositories\CurrenciesRepository;
use Closure;
use Filament\Actions\Contracts\HasRecord;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class CompanyProfile extends Page implements HasRecord
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.app.pages.company-profile';

    public $record = null;

    public $activeTab = 'tabBaseData';

    public ?array $data = [];


    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->isTenantAdmin();
    }

    public static function getSlug(): string
    {
        return 'company';
    }

    public static function getNavigationLabel(): string
    {
        return 'Moja Firma';
    }

    public function getHeading(): string|Htmlable
    {
        return 'Dane firmy';
    }

    public static function getNavigationGroup(): ?string
    {
        return __('app._.settings');
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        unset($data['id'], $data['config'], $data['is_active'], $data['created_at'], $data['updated_at']);

        // Convert metadata to DTO and then back to array for form compatibility
        $metaData = $data['meta']['meta'] ?? [];
        if (!empty($metaData)) {
            try {
                $metadataDto = DTOTenantMetadata::make($metaData);
                $data['meta'] = $metadataDto->toArray();

                // Handle logo for form display
                $logoImage = $metadataDto->getImageByType('logo');
                if ($logoImage) {
                    $data['logo'] = $logoImage->path_filename;
                }
            } catch (\Exception $e) {
                // Fallback to raw data if DTO creation fails
                $data['meta'] = $metaData;
            }
        } else {
            $data['meta'] = [
                'accounting' => ['regon' => '', 'bdo' => ''],
                'bank_accounts' => [],
                'images' => []
            ];
        }

        return $data;
    }


    public function mount(): void
    {
        if (!filled($this->record)) {
            $this->record(Company::with('meta')->find(auth()->user()->installation()));
        }
        $data = $this->mutateFormDataBeforeFill($this->getRecord()->toArray());
        $this->form->fill($data);
    }


    public function form(Form $form): Form
    {
        $mf = $form
            ->model($this->getRecord())
            ->schema([
                Fieldset::make('Dane podstawowe')
                    ->columns(3)
                    ->schema([
                        TextArea::make('name')
                            ->label('Nazwa')
                            ->required()
                            ->columnSpanFull()
                            ->maxLength(120),
                        TextInput::make('postcode')
                            ->label('Kod pocztowy')
                            ->maxLength(100),
                        TextInput::make('city')
                            ->label('Miasto')
                            ->maxLength(100),
                        TextInput::make('phone')
                            ->label('Telefon')
                            ->maxLength(100),
                        TextInput::make('email')
                            ->label('Email')
                            ->email()
                            ->maxLength(60),
                        TextInput::make('contact_name')
                            ->label('Osoba konaktowa'),
                        TextInput::make('website')
                            ->url()
                            ->label('Strona www'),
                        Textarea::make('address')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                    ]),
                Fieldset::make('Logo firmy')
                    ->columns(2)
                    ->visible(fn() => tenant()->hasModule(SystemModules::LOGO))
                    ->schema([
                        FileUpload::make('logo')
                            ->label('Logo firmy')
                            ->image()
                            ->disk('local')
                            ->directory(fn() => "tenants/{$this->getRecord()->hash}/images")
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp'])
                            ->maxSize(2048) // 2MB
//                            ->imageResizeMode('contain')
//                            ->imageCropAspectRatio('16:9')
//                            ->imageResizeTargetWidth('800')
//                            ->imageResizeTargetHeight('450')
                            ->columnSpan(1)
                            ->helperText('Maksymalny rozmiar: 2MB. Dozwolone formaty: JPG, PNG, GIF, WebP'),
                        ViewField::make('logo_preview')
                            ->dehydrated(false)
                        ->view('filament.app.pages.company-logo-preview')

                    ]),
                Fieldset::make('Dane księgowe')
                    ->columns(3)
                    ->schema([
                        Select::make('tax_residency_country')
                            ->label(__('app.partners.create.tax_residency_country'))
                            ->options(
                                TaxResidencyCountries::toArrayWithLabels()
                            )
                            ->formatStateUsing(fn($state) => $state ?? TaxResidencyCountries::PL->name),
                        Select::make('business_type')
                            ->label(__('app.partners.create.business_type'))
                            ->options(PartnerBusinessTypes::toArrayWithLabels())
                            ->default(PartnerBusinessTypes::INDIVIDUAL->value),
                        Select::make('vat_type')
                            ->label(__('app.partners.create.vat_type'))
                            ->options(PartnerVATTypes::toArrayWithLabels())
                            ->default(PartnerVATTypes::LOCAL->value),
                        Select::make('tax_type')
                            ->label(__('app.partners.create.tax_type'))
                            ->options(TaxTypePL::toArrayWithLabels())
                            ->default(TaxTypePL::LINEAR->value),
                        Select::make('accounting_type')
                            ->label(__('app.partners.create.accounting_type'))
                            ->options(AccountingTypesPL::toArrayWithLabels())
                            ->default(AccountingTypesPL::FULL->value),
                        TextInput::make('vat_id')
                            ->label('NIP')
                            ->columnSpan(1),
                        TextInput::make('meta.accounting.regon')
                            ->label('Regon')
                            ->columnSpan('50%'),
                        TextInput::make('meta.accounting.bdo')
                            ->label('Kod BDO'),
                    ]),
                Fieldset::make('Dane bankowe')
                    ->columnSpanFull()
                    ->columns(1)
                    ->schema([
                        Repeater::make('meta.bank_accounts')
                            ->label('Konta bankowe')
                            ->columns(3)
                            ->addActionLabel('Dodaj konto bankowe')
                            ->schema([
                                TextInput::make('account_name')
                                    ->label('Nazwa rachunku')
                                    ->hint('Nazwa rachunku wyświetlana w systemie')
                                    ->columnSpan('50%'),
                                TextInput::make('bank_name')
                                    ->label('Nazwa banku')
                                    ->columnSpan('50%'),
                                TextInput::make('bank_account')
                                    ->label('Numer konta'),
                                TextInput::make('bank_swift')
                                    ->label('Kod SWIFT'),
                                TextInput::make('bank_iban')
                                    ->label('Kod IBAN'),
                                Select::make('bank_currency')
                                    ->label('Waluta')
                                    ->options(
                                        collect(CurrenciesRepository::getSystemCurrencies())
                                            ->mapWithKeys(
                                                static fn($currency) => [
                                                    $currency->alphabeticCode => $currency->getLabel()
                                                ]
                                            )
                                            ->toArray()
                                    )
                                    ->required()
                                    ->default('PLN'),
                            ]),
                    ]),
                Fieldset::make('Dane systemowe')
                    ->columns(2)
                    ->schema([
                        TextInput::make('system_domain')
                            ->readOnly()
                            ->label('Domena w systemie'),
                    ]),
            ])
            ->statePath('data');

        return $mf;
    }


    public function save()
    {
        $data = $this->getForm('form')->getState();
        $meta = $data['meta'] ?? [];
        $logo = $data['logo'] ?? null;
        unset($data['meta'], $data['logo']);

        // Update basic tenant data
        $this->getRecord()->update($data);

        // Handle logo upload
        if ($logo) {
            try {
                // Create DTO from existing metadata
                $metadataDto = DTOTenantMetadata::make($meta);

                // Handle logo file - check if it's a new upload or existing file
                if (is_array($logo)) {
                    // New upload - $logo is an array with file info
                    $logoPath = $logo[0] ?? $logo; // Get first file if array
                } else {
                    // Existing file or string filename
                    $logoPath = $logo;
                }

                $filename = pathinfo($logoPath, PATHINFO_BASENAME);

                // Create image DTO
                $imageData = new DTOImageData(
                    type: 'logo',
                    filename: $filename,
                    path_filename: $logoPath,
                    upload_timestamp: now()->toISOString()
                );

                // Add or update logo in metadata
                $metadataDto = $metadataDto->addOrUpdateImage($imageData);
                $meta = $metadataDto->toArray();
            } catch (\Exception $e) {
                Notification::make()
                    ->title('Błąd podczas zapisywania logo')
                    ->body('Wystąpił błąd podczas przetwarzania logo: ' . $e->getMessage())
                    ->danger()
                    ->send();
                return false;
            }
        } else {
            // Remove logo if it was removed from the form
            if ($oldLogo = $this->getLogoImage()) {
                try {
                    $logoPath = storage_path("app/tenants/{$this->getRecord()->hash}/images/{$oldLogo->filename}");
                    if (\Illuminate\Support\Facades\File::exists($logoPath)) {
                        \Illuminate\Support\Facades\File::delete($logoPath);
                    }
                } catch (\Exception $e) {
                    // Log error and continue
                    \Illuminate\Support\Facades\Log::warning('Failed to delete logo: ' . $e->getMessage());
                }
                $metadataDto = DTOTenantMetadata::make($meta);
                $metadataDto = $metadataDto->removeImageByType('logo');
                $meta = $metadataDto->toArray();
            }
        }

        // Handle metadata using DTO
        if ($meta) {
            try {
                // Create DTO from form data
                $metadataDto = DTOTenantMetadata::make($meta);

                // Validate the DTO
                if (!$metadataDto->isValid()) {
                    $errors = $metadataDto->validate();
                    Notification::make()
                        ->title('Błędy walidacji')
                        ->body('Sprawdź poprawność danych: ' . implode(', ', $errors))
                        ->danger()
                        ->send();
                    return false;
                }

                // Save validated metadata to database
                $this->getRecord()
                    ->meta()
                    ->updateOrCreate(
                        ['tenant_id' => $this->getRecord()->id],
                        ['meta' => $metadataDto->toArray()]
                    );

                \tenant(true);
            } catch (\Exception $e) {
                Notification::make()
                    ->title('Błąd podczas zapisywania')
                    ->body('Wystąpił błąd podczas przetwarzania danych: ' . $e->getMessage())
                    ->danger()
                    ->send();
                return false;
            }
        }

        Notification::make()
            ->title('Zapisano pomyślnie')
            ->success()
            ->send();
        return true;
    }

    public function getFormAction(): array
    {
        return [
            \Filament\Actions\Action::make('save')
                ->submit('form')
                ->label('Zapisz')
        ];
    }


    public function record(Model|Closure|null $record): static
    {
        $this->record = $record;
        return $this;
    }

    public function getRecord(): ?Model
    {
        return $this->record;
    }

    public function getRecordTitle(): ?string
    {
        return "Moja firma";
    }

    public function hasRecord(): bool
    {
        return filled($this->getRecord());
    }

    /**
     * Get the current tenant metadata as DTO
     */
    public function getTenantMetadataDto(): ?DTOTenantMetadata
    {
        $metaData = $this->getRecord()->meta?->meta ?? [];

        if (empty($metaData)) {
            return null;
        }

        try {
            return DTOTenantMetadata::make($metaData);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Get bank accounts from current tenant metadata
     */
    public function getBankAccounts(): \Illuminate\Support\Collection
    {
        $dto = $this->getTenantMetadataDto();
        return $dto ? $dto->getBankAccounts() : collect();
    }

    /**
     * Get accounting data from current tenant metadata
     */
    public function getAccountingData(): ?DTOAccountingData
    {
        $dto = $this->getTenantMetadataDto();
        return $dto ? $dto->getAccounting() : null;
    }

    /**
     * Get images from current tenant metadata
     */
    public function getImages(): \Illuminate\Support\Collection
    {
        $dto = $this->getTenantMetadataDto();
        return $dto ? $dto->getImages() : collect();
    }

    /**
     * Get logo image data from current tenant metadata
     */
    public function getLogoImage(): ?\App\Models\DTOImageData
    {
        $dto = $this->getTenantMetadataDto();
        return $dto ? $dto->getImageByType('logo') : null;
    }

    /**
     * Check if tenant has a logo
     */
    public function hasLogo(): bool
    {
        return $this->getLogoImage() !== null;
    }
}
