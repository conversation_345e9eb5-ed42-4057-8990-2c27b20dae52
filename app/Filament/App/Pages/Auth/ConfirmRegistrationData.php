<?php

namespace App\Filament\App\Pages\Auth;

use App\Enums\AccountingTypesPL;
use App\Enums\Countries;
use App\Enums\PartnerBusinessTypes;
use App\Enums\PartnerVATTypes;
use App\Enums\PlanType;
use App\Enums\Roles;
use App\Enums\SubscriptionStatus;
use App\Enums\SystemModules;
use App\Enums\TaxResidencyCountries;
use App\Enums\TaxTypePL;
use App\Events\Registration\RegistrationFinished;
use App\Filament\App\Resources\PartnerResource;
use App\Helpers\StringHelper;
use App\Models\Plan;
use App\Models\Registration;
use App\Models\Tenant;
use App\Models\User;
use App\Repositories\CurrenciesRepository;
use App\Repositories\DocumentSeriesRepository;
use App\Repositories\GUSRepository;
use App\Repositories\SubscriptionsRepository;
use Filament\Facades\Filament;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Pages\Concerns\HasRoutes;
use Filament\Pages\Page;
use Filament\Pages\SimplePage;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\HtmlString;
use Illuminate\Validation\ValidationException;

class ConfirmRegistrationData extends SimplePage
{
    use InteractsWithForms, HasRoutes;

    protected static string $view = 'filament.app.pages.auth.confirm-user-data';

    public ?string $email = null;

    protected ?string $heading = 'Potwierdź swoje dane';


    public array $data = [];

    public function mount(string $hash): mixed
    {

        $registration = Registration::where('registration_hash', $hash)->first();

        if (!$registration) {
            return redirect(Filament::getRegistrationUrl());
        }

        if (!$registration->isConfirmed()) {
            $this->redirect(ConfirmRegistration::getUrl(['hash' => $registration->registration_hash]));
        }

        if ($registration->isFinished()) {
            $this->redirect(Filament::getLoginUrl());
        }

        if (Session::get('registration_code') !== $registration->confirmation_code) {
            $this->redirect(ConfirmRegistration::getUrl(['hash' => $registration->registration_hash]));
        }

        $this->email = $registration->email;

        $this->form->fill($registration->data);

        return true;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Wizard::make([
                    Wizard\Step::make('Dane Osobowe')
                        ->schema([
                            TextInput::make('user.name')
                                ->required()
                                ->validationMessages([
                                    'required' => 'Imię jest wymagane.',
                                    'max' => 'Imię może mieć najwyżej :max znaków.',
                                ])
                                ->label(__('app.users.profile.name'))
                                ->maxLength(100),
                            TextInput::make('user.surname')
                                ->required()
                                ->validationMessages([
                                    'required' => 'Nazwisko jest wymagane.',
                                    'max' => 'Nazwisko może mieć najwyżej :max znaków.',
                                ])
                                ->label(__('app.users.profile.surname'))
                                ->maxLength(255),
                            TextInput::make('user.password')
                                ->required()
                                ->label(__('app.users.password'))
                                ->password()
                                ->validationMessages([
                                    'required' => 'Haslo jest wymagane.',
                                    'max' => 'Hasło może mieć najwyżej :max znaków.',
                                    'min' => 'Hasło musi mieć co najmniej :min znaków.',
                                ])
                                ->minLength(10)
                                ->maxLength(255),
                            TextInput::make('user.password_confirmation')
                                ->required()
                                ->label(__('app.users.password_confirmation'))
                                ->password()
                                ->validationMessages([
                                    'required' => 'Potwierdzenie hasla jest wymagane.',
                                    'max' => 'Hasło może mieć najwyżej :max znaków.',
                                    'same' => 'Potwierdzenie hasla musi być takie samo jak hasło.',
                                ])
                                ->same('user.password')
                                ->maxLength(255),
                            TextInput::make('user.adress')
                                ->label(__('app.users.profile.address'))
                                ->validationMessages([
                                    'max' => 'Adres może mieć najwyżej :max znaków.',
                                ])
                                ->maxLength(255),
                            TextInput::make('user.number')
                                ->label(__('app.users.profile.number'))
                                ->maxLength(255)
                                ->validationMessages([
                                    'max' => 'Adres może mieć najwyżej :max znaków.',
                                ])
                                ->numeric(),
                        ]),
                    Wizard\Step::make('Dane księgowe')
                        ->columns(2)
                        ->schema([
                            Select::make('company.tax_residency_country')
                                ->required()
                                ->validationMessages([
                                    'required' => 'Kraj rezydencji podatkowej jest wymagany.',
                                ])
                                ->label(__('app.partners.create.tax_residency_country'))
                                ->options(
                                    TaxResidencyCountries::toArrayWithLabels()
                                )
                                ->formatStateUsing(fn($state) => $state ?? TaxResidencyCountries::PL->name),
                            TextInput::make('company.vat_id')
                                ->required()
                                ->live(true)
                                ->afterStateUpdated(
                                    function (Set $set, $state) {
                                        if (blank($state)) {
                                            return;
                                        }
                                        $set('company.vat_id', StringHelper::extractDigits($state));
                                    }
                                )
                                ->validationMessages([
                                    'required' => 'Numer NIP jest niezbędny.',
                                ])
                                ->suffixAction(
                                    fn(Set $set) => Action::make('gus-import')
                                        ->label('Import z GUS')
                                        ->hiddenLabel(false)
                                        ->disabled(
                                            function (Get $get) {
                                                return $get('company.tax_residency_country') !== Countries::PL->name;
                                            }
                                        )
                                        ->icon('heroicon-o-arrow-down-tray')
                                        ->action(function (Get $get, Set $set) {
                                            if (blank($get('company.vat_id'))) {
                                                return;
                                            }

                                            $nip = StringHelper::extractDigits($get('company.vat_id'));
                                            if (blank($nip) || strlen($nip) !== 10) {
                                                Notification::make()->title('Podaj prawidłowy NIP')
                                                    ->danger()
                                                    ->send();
                                                return;
                                            }

                                            $company = GUSRepository::findByNip($nip);
                                            if (blank($company)) {
                                                Notification::make()
                                                    ->title('Partner z podanego NIP nie został znaleziony')
                                                    ->danger()
                                                    ->send();
                                                return;
                                            }
                                            $this->mapGUSResponseToFormData($company, $set);
                                            Notification::make()->title('Dane zostały zaimportowane')
                                                ->success()
                                                ->send();
                                        })
                                )
                                ->label('NIP'),
                            Select::make('company.business_type')
                                ->required()
                                ->validationMessages([
                                    'required' => 'Rodzaj działalności jest wymagany.',
                                ])
                                ->label(__('app.partners.create.business_type'))
                                ->options(PartnerBusinessTypes::toArrayWithLabels())
                                ->default(PartnerBusinessTypes::INDIVIDUAL->value),
                            Select::make('company.vat_type')
                                ->required()
                                ->validationMessages([
                                    'required' => 'Status podatnika VAT jest wymagany.',
                                ])
                                ->label(__('app.partners.create.vat_type'))
                                ->options(PartnerVATTypes::toArrayWithLabels())
                                ->default(PartnerVATTypes::LOCAL->value),
                            Select::make('company.tax_type')
                                ->label(__('app.partners.create.tax_type'))
                                ->options(TaxTypePL::toArrayWithLabels())
                                ->default(TaxTypePL::LINEAR->value),
                            Select::make('company.accounting_type')
                                ->label(__('app.partners.create.accounting_type'))
                                ->options(AccountingTypesPL::toArrayWithLabels())
                                ->default(AccountingTypesPL::FULL->value),
                            TextInput::make('company.meta.accounting.regon')
                                ->label('Regon'),
                            TextInput::make('company.meta.accounting.bdo')
                                ->maxLength(10)
                                ->label('Kod BDO'),
                        ]),
                    Wizard\Step::make('Dane Firmy')
                        ->columns(2)
                        ->schema([
                            TextArea::make('company.name')
                                ->label('Nazwa')
                                ->validationMessages([
                                    'required' => 'Nazwa firmy jest wymagana.',
                                    'max' => 'Nazwa firmy może mieć najwyżej :max znaków.',
                                ])
                                ->required()
                                ->columnSpanFull()
                                ->maxLength(120),
                            TextInput::make('company.postcode')
                                ->required()
                                ->validationMessages([
                                    'required' => 'Kod pocztowy jest wymagana.',
                                    'max' => 'Kod pocztowy może mieć najwyżej :max znaków.',
                                ])
                                ->label('Kod pocztowy')
                                ->maxLength(20),
                            TextInput::make('company.city')
                                ->required()
                                ->label('Miasto')
                                ->validationMessages([
                                    'required' => 'Nazwa miasta jest wymagana.',
                                    'max' => 'Nazwa miasta może mieć najwyżej :max znaków.',
                                ])
                                ->maxLength(100),
                            TextInput::make('company.phone')
                                ->label('Telefon')
                                ->validationMessages([
                                    'max_digits' => 'Numer telefonu może mieć najwyżej :max znaków.',
                                    'min_digits' => 'Numer telefonu misu mieć co najmniej :min znaków.',
                                ])
                                ->numeric()
                                ->minLength(8)
                                ->maxLength(100),
                            TextInput::make('company.email')
                                ->label('Email')
                                ->email()
                                ->maxLength(60),
                            TextInput::make('company.contact_name')
                                ->label('Osoba konaktowa'),
                            TextInput::make('company.website')
                                ->url()
                                ->label('Strona www'),
                            Textarea::make('company.address')
                                ->label('Adres')
                                ->validationMessages([
                                    'required' => 'Adres firmy jest wymagany.',
                                    'max' => 'Adres firmy może mieć najwyżej :max znaków.',
                                ])
                                ->required()
                                ->maxLength(500)
                                ->columnSpanFull(),
                        ]),
                    Wizard\Step::make('Dane bankowe')
                        ->schema([
                            Repeater::make('company.meta.bank_accounts')
                                ->label('Konta bankowe')
                                ->columns(2)
                                ->addActionLabel('Dodaj nowe konto bankowe')
                                ->schema([
                                    TextInput::make('account_name')
                                        ->label('Nazwa rachunku wyświetlana w systemie'),
                                    TextInput::make('bank_name')
                                        ->label('Nazwa banku'),
                                    TextInput::make('bank_account')
                                        ->mask('99 9999 9999 9999 9999 9999 9999')
                                        ->label('Numer konta'),
                                    TextInput::make('bank_swift')
                                        ->label('Kod SWIFT/BIC'),
                                    TextInput::make('bank_iban')
                                        ->label('Kod IBAN'),
                                    Select::make('bank_currency')
                                        ->label('Waluta')
                                        ->options(
                                            collect(CurrenciesRepository::getSystemCurrencies())
                                                ->mapWithKeys(
                                                    static fn($currency) => [
                                                        $currency->alphabeticCode => $currency->getLabel()
                                                    ]
                                                )
                                                ->toArray()
                                        )
                                        ->required()
                                        ->default('PLN'),
                                ]),
                        ]),
                    $this->getPlanSelectionStep(),
                ])
                    ->submitAction(
                        new HtmlString(Blade::render(<<<BLADE
    <x-filament::button
        type="submit"
        size="sm"
    >
        Potwierdź rejestrację
    </x-filament::button>
BLADE
                        ))
                    )
            ])
            ->statePath('data');
    }

    protected function mapGUSResponseToFormData(array $company, Set $set): void
    {
        $address = $company['street'] . ' ' . $company['propertyNumber'];
        if (filled($company['apartmentNumber'])) {
            $address .= ', lok. ' . $company['apartmentNumber'];
        }
        $btype = $company['type'] === 'f' ?
            PartnerBusinessTypes::INDIVIDUAL->value :
            PartnerBusinessTypes::BUSINESS->value;

        $set('company.business_type', $btype);
        $set('company.name', $company['name']);
        $set('company.postcode', $company['zipCode']);
        $set('company.city', filled($company['postCity']) ? $company['postCity'] : $company['city']);
        $set('company.address', $address);
    }

    public function getPlanSelectionStep(): Wizard\Step
    {
        return Wizard\Step::make('Wybór planu')
            ->schema([
                Section::make('Wybierz plan subskrypcji')
                    ->description('Wybierz plan, który najlepiej odpowiada Twoim potrzebom')
                    ->schema([
                        Radio::make('selected_plan_id')
                            ->label('')
                            ->required()
                            ->validationMessages([
                                'required' => 'Wybór planu jest wymagany.',
                            ])
                            ->options($this->getPlanOptions())
                            ->descriptions($this->getPlanDescriptions())
                            ->columns(1)
                    ])
            ]);
    }

    public function getPlanOptions(): array
    {
        return Plan::where('is_active', true)
            ->get()
            ->mapWithKeys(function (Plan $plan) {
                $features = collect($plan->features)
                    ->map(fn($featureId) => SystemModules::get_label($featureId))
                    ->filter()
                    ->join(', ');

                $priceText = $plan->price > 0
                    ? number_format($plan->price, 2, ',', ' ') . ' PLN / ' . $plan->period->value . ' miesięcy'
                    : 'Bezpłatny';

                return [
                    $plan->id => $plan->name . ' - ' . $priceText
                ];
            })
            ->toArray();
    }

    public function getPlanDescriptions(): array
    {
        return Plan::where('is_active', true)
            ->get()
            ->mapWithKeys(function (Plan $plan) {
                $features = collect($plan->features)
                    ->map(fn($featureId) => SystemModules::get_label($featureId))
                    ->filter()
                    ->join(', ');

                $description = $plan->description;
                if ($features) {
                    $description .= "\n\nDostępne funkcje: " . $features;
                }

                return [$plan->id => $description];
            })
            ->toArray();
    }

    public function confirm(): mixed
    {
        $data = $this->form->getState();

        $registration = Registration::where('email', $this->email)->first();

        if (!$registration) {
            abort(404);
        }

        if ($registration->isFinished()) {
            $this->redirect(Filament::getLoginUrl());
        }

        $registration->update([
            'data' => Arr::except($data, ['user.password', 'user.password_confirmation'])
        ]);

        $dataCollection = collect($data);
        if ($dataCollection->get('user')['password'] !== $dataCollection->get('user')['password_confirmation']) {
            abort(404);
        }

        /**
         * @var Tenant $tenant
         * @var User $user
         * @var Plan $selectedPlan
         */
        $tenant = null;
        $user = null;
        $selectedPlan = null;

        if (!$this->buildSystemEntities($registration, $dataCollection, $tenant, $user, $selectedPlan)) {
            abort(400, 'Wystąpił błąd podczas rejestracji. Proszę spróbować ponownie.');
        }

        $subRepo = new SubscriptionsRepository();
        $subscription = $subRepo->createSubscriptionWithPayment($user, $selectedPlan);

        $continuePaymentUrl = null;
        if (!$subRepo->isPaymentFinished) {
            $continuePaymentUrl = $subRepo->continuePaymentUrl;
        }

        $meta = $tenant->meta->meta;
        $meta['subscription']['subscription_id'] = $subscription->id;
        $tenant->meta->update(['meta' => $meta]);

        DocumentSeriesRepository::seedDefaultSeries($tenant, 'trade');

        Auth::login($user);

        RegistrationFinished::dispatch($registration, $user);

        Session::forget('registration_code');
        if (filled($continuePaymentUrl)) {
            return redirect($continuePaymentUrl, true);
        }

        $this->redirect(Filament::getLoginUrl());
        return true;
    }

    protected function buildSystemEntities($registration, $dataCollection, &$tenant, &$user, &$selectedPlan): bool
    {
        try {
            DB::transaction(
                static function () use ($registration, $dataCollection, &$tenant, &$user, &$selectedPlan) {
                    $companyData = $dataCollection->get('company');
                    $userData = $dataCollection->get('user');
                    $selectedPlanId = $dataCollection->get('selected_plan_id');

                    $selectedPlan = Plan::find($selectedPlanId);
                    if (!$selectedPlan || !$selectedPlan->is_active) {
                        throw new \Exception('Invalid plan selected');
                    }

                    $tenantConfig = [
                        'modules' => [],
                        'selected_plan_id' => $selectedPlanId,
                        'subscription_status' => $selectedPlan->type === PlanType::TRIAL ?
                            SubscriptionStatus::TRIAL->name :
                            SubscriptionStatus::NEW->name,
                    ];

                    $tenant = Tenant::create([
                        ...Arr::except($companyData, 'meta'),
                        'hash' => bin2hex(random_bytes(16)),
                        'config' => $tenantConfig,
                        'is_active' => true,
                    ]);

                    $tenant->meta()->create(
                        Arr::only($companyData, 'meta')
                    );

                    $user = User::create([
                        'name' => $registration->email,
                        'email' => $registration->email,
                        'password' => Hash::make($userData['password']), // Random password, user will need to reset
                        'active' => true,
                    ]);

                    $user->assignRole(Roles::TENANT_ADMIN);

                    $user->profile()->create([
                        ...Arr::except($userData, ['password', 'password_confirmation']),
                        'lang' => 'pl'
                    ]);

                    $user->tenant()->attach($tenant->id);

                    $existingMeta = $tenant->meta?->meta ?? [];
                    $existingMeta['subscription'] = [
                        'plan_id' => $selectedPlan->id,
                        'plan_name' => $selectedPlan->name,
                        'selected_at' => now()->toISOString(),
                    ];

                    if ($tenant->meta) {
                        $tenant->meta->update(['meta' => $existingMeta]);
                    } else {
                        $tenant->meta()->create(['meta' => $existingMeta]);
                    }

                    $partner = $tenant->convertToPartner(systemTenant()->id, true);

                    $tenant->update([
                        'system_partner' => $partner->id,
                    ]);

                    $registration->update([
                        'finished_at' => now(),
                    ]);
                },
                2
            );
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return false;
        }
        return true;
    }

    public static function getUrl(
        array $parameters = []
    ): string {
        return (string)route('filament.app.auth.register.confirm-data', $parameters);
    }

    public function getMaxWidth(): MaxWidth|string|null
    {
        return MaxWidth::FitContent;
    }
}
