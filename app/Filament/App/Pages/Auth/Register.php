<?php

namespace App\Filament\App\Pages\Auth;

use App\Events\Registration\RegistrationCreated;
use App\Helpers\StringHelper;
use App\Mail\RegistrationConfirmation;
use App\Models\Registration;
use App\Models\User;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Set;
use Filament\Http\Responses\Auth\Contracts\RegistrationResponse;
use Filament\Pages\Auth\Register as BaseRegister;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class Register extends BaseRegister
{

    public function mount(): void
    {
        if (Str::of(config('app.url'))->rtrim('/')->__toString() !== request()->getSchemeAndHttpHost()) {
            abort(404);
        }
        parent::mount();
    }


    public function form(Form $form): Form
    {
        return $form
            ->statePath('data')
            ->schema([
                TextInput::make('email')
                    ->label('Email address')
                    ->email()
                    ->required()
                    ->maxLength(255)
                    ->validationMessages([
                        'required' => 'Adres email jest wymagany.',
                    ])
                    ->rules([
                        fn(): \Closure => static function (string $attribute, $value, \Closure $fail) {
                            $user = User::query()->where('email', $value)->active()->exists();
                            if ($user) {
                                $fail(self::getErrorMessage());
                            }

                            $registration = Registration::query()
                                ->where('email', $value)
                                ->first();
                            if ($registration) {
                                if ($registration->isConfirmed()) {
                                    $fail(self::getErrorMessage());
                                } else {
                                    $registration->delete();
                                }
                            }

                            return true;
                        }
                    ]),
                TextInput::make('vat_id')
                    ->label('NIP firmy')
                    ->required()
                    ->live(true)
                    ->afterStateUpdated(
                        function (Set $set, $state, $component) {
                            if (blank($state)) {
                                return;
                            }
                            $NIP = StringHelper::extractDigits($state);
                            $set('vat_id', $NIP);
                        }
                    )
                    ->rules([
                        fn(): \Closure => static function (string $attribute, $value, \Closure $fail) {
                            if (strlen($value) !== 10) {
                                $fail('Podaj prawidłowy NIP firmy (10 cyfr).');
                            }
                            if (false === is_numeric($value)) {
                                $fail('NIP firmy ma 10 cyfr');
                            }
                            return true;
                        }
                    ])
                    ->validationMessages([
                        'required' => 'NIP firmy jest wymagany.'
                    ])
                    ->helperText('NIP firmy (tylko 10 cyfr)'),
            ]);
    }

    public static function getErrorMessage(): string
    {
        return 'Adres nie może być użyty do rejestracji. '
        .'Użytkownik istnieje lub proces rejestracji został już rozpoczęty.';
    }

    public function register(): ?RegistrationResponse
    {
        $data = $this->form->getState();

        $registration = Registration::create([
            'email' => $data['email'],
            'vat_id' => $data['vat_id'],
            'registration_hash' => bin2hex(random_bytes(32)),
            'confirmation_code' => Registration::generateConfirmationCode(),
            'code_sent_at' => now(),
        ]);

        $confirmationUrl = ConfirmRegistration::getUrl(['hash' => $registration->registration_hash]);

        Mail::to($registration->email)
            ->send(new RegistrationConfirmation($registration, $confirmationUrl));

        RegistrationCreated::dispatch($registration);

        $this->redirect($confirmationUrl);

        return null;
    }
}
