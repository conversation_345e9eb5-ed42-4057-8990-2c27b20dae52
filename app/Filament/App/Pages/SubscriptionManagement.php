<?php

namespace App\Filament\App\Pages;

use App\Enums\PlanType;
use App\Enums\SubscriptionStatus;
use App\Enums\SystemModules;
use App\Models\DTOTenantMetadata;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\Tenant;
use App\Repositories\SubscriptionsRepository as SubRepository;
use Filament\Actions\Action;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Section as FormSection;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Contracts\HasInfolists;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Support\Enums\FontWeight;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Collection;
use Illuminate\Support\HtmlString;

class SubscriptionManagement extends Page
{
    use InteractsWithForms, InteractsWithInfolists;

    protected static ?string $navigationIcon = 'heroicon-o-credit-card';
    protected static string $view = 'filament.app.pages.subscription-management';
    protected static ?string $navigationGroup = 'Ustawienia';
    protected static ?int $navigationSort = 10;

    public ?Tenant $tenant = null;
    public ?Subscription $currentSubscription = null;
    public Collection $availablePlans;
    protected ?DTOTenantMetadata $tenantMetadata = null;

    public bool $redirect = false;
    public string $redirectUrl = '';

    public bool $trialPlanUsed = false;

    // Properties for renewal payment handling
    public bool $isPaymentFinished = false;
    public ?string $continuePaymentUrl = null;
    public ?Subscription $pendingSubscription = null;

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->isTenantAdmin();
    }

    public static function getNavigationLabel(): string
    {
        return 'Zarządzanie subskrypcją';
    }

    public function getHeading(): string|Htmlable
    {
        return 'Zarządzanie subskrypcją';
    }

    public function getSubheading(): string|Htmlable|null
    {
        return 'Zarządzaj swoją subskrypcją i wybierz plan odpowiadający Twoim potrzebom';
    }

    public function mount(): void
    {
        $this->tenant = auth()->user()->getTenant();
        $this->loadCurrentSubscription();
        $this->trialPlanUsed();
        $this->loadAvailablePlans();
        $this->loadTenantMetadata();
        $this->pendingSubscription = $this->hasPendingSubscription();
    }

    protected function trialPlanUsed()
    {
        $this->trialPlanUsed  = Subscription::where('tenant_id', $this->tenant->id)
                ->where('plan_id', 1)
                ->whereNotNull('ended_at')
                ->count() > 0;
    }

    protected function hasPendingSubscription(): ?Subscription
    {
        return $this->tenant?->getPendingSubscription();
    }

    protected function trialPlansAvailable(): bool
    {
        return $this->tenant?->subscription?->selected_at ? true : false;
    }

    protected function loadCurrentSubscription(): void
    {
        $this->currentSubscription = $this->tenant?->subscription;
    }

    protected function loadAvailablePlans(): void
    {
        $this->availablePlans = Plan::where('is_active', true)
            ->orderBy('price')
            ->get();
        if ($this->trialPlanUsed && $this->currentSubscription?->plan->type !== PlanType::TRIAL) {
            $this->availablePlans = $this->availablePlans->reject(fn($plan) => $plan->type === PlanType::TRIAL);
        }
    }

    protected function loadTenantMetadata(): void
    {
        $metaData = $this->tenant?->meta?->meta ?? [];

        if (!empty($metaData)) {
            try {
                $this->tenantMetadata = DTOTenantMetadata::make($metaData);
            } catch (\Exception $e) {
                $this->tenantMetadata = null;
            }
        }
    }

    protected function getRenewalPlanOptions(): array
    {
        return $this->availablePlans
            ->filter(fn($plan) => $plan->type !== PlanType::TRIAL)
            ->mapWithKeys(function (Plan $plan) {
                $periodDeclination = match ($plan->period->value) {
                    1 => 'miesiąc',
                    2 => 'miesiące',
                    3 => 'miesiące',
                    4 => 'miesiące',
                    default => 'miesięcy',
                };

                $priceText = number_format($plan->price, 2, ',', ' ') .
                    ' PLN / ' .
                    $plan->period->value .
                    ' ' .
                    $periodDeclination;

                $isSame = SubRepository::isSamePlan($plan, $this->currentSubscription);
                $label = $plan->name .
                    ' - ' .
                    $priceText . ($isSame ? ' <strong>(kontynuacja)</strong>' : ' <strong>(zmiana planu)</strong>');

                return [$plan->id => new HtmlString($label)];
            })->toArray();
    }

    protected function getRenewalPlanDescriptions(): array
    {
        return $this->availablePlans
            ->filter(fn($plan) => $plan->type !== PlanType::TRIAL)
            ->mapWithKeys(function (Plan $plan) {
                $features = $this->formatPlanFeatures($plan->features ?? []);
                $startDate = SubRepository::calculateRenewalStartDate($plan, $this->currentSubscription);

                $description = $plan->description . "<br>";
                $description .= "Dostępne funkcje: " . implode(', ', $features) . "<br>";
                $description .= "Data rozpoczęcia: " . $startDate->format('d.m.Y') . "<br>";

                return [$plan->id => new HtmlString($description)];
            })->toArray();
    }

    protected function createRenewalSubscription(Plan $plan): void
    {
        $subRepo = new SubRepository();
        $subRepo->renewSubscriptionWithPayment(auth()->user(), $plan, $this->currentSubscription);
        $this->isPaymentFinished = $subRepo->isPaymentFinished;
        $this->continuePaymentUrl = $subRepo->continuePaymentUrl;
    }

    public function currentSubscriptionInfolist(Infolist $infolist): Infolist
    {
        if (!$this->currentSubscription) {
            return $infolist->schema([
                Section::make('Brak aktywnej subskrypcji')
                    ->description('Nie masz obecnie aktywnej subskrypcji. Wybierz plan poniżej, aby rozpocząć.')
                    ->schema([])
            ]);
        }

        return $infolist
            ->record($this->currentSubscription)
            ->schema([
                Section::make('Aktualna subskrypcja')
                    ->description('Szczegóły Twojej obecnej subskrypcji')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextEntry::make('plan.name')
                                    ->label('Plan')
                                    ->formatStateUsing(
                                        fn($state, $record) => $state . ' (subskrypcja: ' . $record->getOrderId() . ')'
                                    )
                                    ->weight(FontWeight::Bold),
                                TextEntry::make('status')
                                    ->label('Status')
                                    ->badge()
                                    ->color(fn($state) => match ($state) {
                                        SubscriptionStatus::ACTIVE => 'success',
                                        SubscriptionStatus::NEW => 'warning',
                                        SubscriptionStatus::TRIAL => 'info',
                                        SubscriptionStatus::EXPIRED => 'danger',
                                        SubscriptionStatus::CANCELED => 'gray',
                                        default => 'gray'
                                    })
                                    ->formatStateUsing(fn($state) => $state->label()),
                                TextEntry::make('price')
                                    ->label('Cena')
                                    ->formatStateUsing(
                                        fn($state) => number_format($state / 100, 2, ',', ' ') . ' PLN'
                                    ),
                                TextEntry::make('starts_at')
                                    ->label('Data rozpoczęcia')
                                    ->date('d.m.Y', 'Europe/Warsaw'),
                                TextEntry::make('ends_at')
                                    ->label('Data zakończenia')
                                    ->date('d.m.Y', 'Europe/Warsaw'),
                                TextEntry::make('plan.period')
                                    ->label('Okres')
                                    ->formatStateUsing(fn($state) => $state->value . ' miesięcy'),
                            ]),
                        TextEntry::make('plan.description')
                            ->label('Opis planu')
                            ->columnSpanFull()
                            ->hidden(fn($record) => empty($record->plan->description)),
                        TextEntry::make('pendingSubscription')
                            ->label('Plan oczekujący')
                            ->badge()
                            ->color('success')
                            ->state(fn() => 'Masz opłacony kolejny plan: ' . $this->pendingSubscription->plan->name)
                            ->columnSpanFull()
                            ->hidden(fn($record) => empty($this->pendingSubscription)),
                    ])
            ]);
    }

    protected function getHeaderActions(): array
    {
        $actions = [];

        if ($this->shouldShowContinuePaymentButton()) {
            $latestPayment = $this->currentSubscription->payments()->latest()->first();

            if ($latestPayment && $latestPayment->getFinishPaymentLink()) {
                $actions[] = Action::make('continuePayment')
                    ->label('Kontynuuj płatność')
                    ->icon('heroicon-o-credit-card')
                    ->color('warning')
                    ->url($latestPayment->getFinishPaymentLink())
                    ->openUrlInNewTab();
            }
        }

        if ($this->shouldShowRenewalButton()) {
            $actions[] = Action::make('renewSubscription')
                ->label('Przedłuż Subskrypcję')
                ->icon('heroicon-o-arrow-path')
                ->color('success')
                ->form([
                    FormSection::make('Wybierz plan do przedłużenia')
                        ->description('Wybierz plan, na który chcesz przedłużyć swoją subskrypcję')
                        ->schema([
                            Radio::make('selected_plan_id')
                                ->label('')
                                ->required()
                                ->validationMessages([
                                    'required' => 'Wybór planu jest wymagany.',
                                ])
                                ->options($this->getRenewalPlanOptions())
                                ->descriptions($this->getRenewalPlanDescriptions())
                                ->columns(1)
                                ->default($this->currentSubscription->plan_id)
                        ])
                ])
                ->modalHeading('Przedłuż subskrypcję')
                ->modalSubmitActionLabel('Przedłuż subskrypcję')
                ->modalWidth('4xl')
                ->action(function (array $data) {
                    $selectedPlan = Plan::findOrFail($data['selected_plan_id']);

                    try {
                        $this->createRenewalSubscription($selectedPlan);

                        if ($this->isPaymentFinished) {
                            tenant(true);
                            Notification::make()
                                ->title('Subskrypcja została przedłużona')
                                ->body('Twoja subskrypcja została pomyślnie przedłużona.')
                                ->success()
                                ->send();
                            $this->redirect(self::getUrl());
                            return;
                        }

                        if ($this->continuePaymentUrl) {
                            $this->redirectUrl = $this->continuePaymentUrl;
                            $this->redirect = true;
                        }
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Błąd podczas przedłużania subskrypcji')
                            ->body('Wystąpił błąd podczas przedłużania subskrypcji. Spróbuj ponownie.')
                            ->danger()
                            ->send();
                    }
                })
                ->after(fn() => $this->redirect ? $this->redirect($this->redirectUrl) : true);
        }

        if ($this->shouldShowCancelButton()) {
            $actions[] = Action::make('cancelSubscription')
                ->label('Anuluj subskrypcję')
                ->icon('heroicon-o-x-circle')
                ->color('danger')
                ->requiresConfirmation()
                ->modalHeading('Anuluj subskrypcję')
                ->modalDescription('Czy na pewno chcesz anulować swoją subskrypcję? Ta akcja nie może zostać cofnięta.')
                ->modalSubmitActionLabel('Tak, anuluj')
                ->action(function () {
                    $subRepo = new SubRepository();
                    $subRepo->cancelSubscription($this->currentSubscription);
                    tenant(true);
                    Notification::make()
                        ->title('Subskrypcja została anulowana')
                        ->success()
                        ->sendToDatabase(auth()->user());
                })
                ->after(fn() => $this->redirect(self::getUrl()));
        }

        return $actions;
    }

    public function getAvailablePlansData(): array
    {
        return $this->availablePlans->map(function (Plan $plan) {
            $features = $this->formatPlanFeatures($plan->features ?? []);
            $periodDeclination = match ($plan->period->value) {
                1 => 'miesiąc',
                2 => 'miesiące',
                3 => 'miesiące',
                4 => 'miesiące',
                default => 'miesięcy',
            };

            return [
                'id' => $plan->id,
                'name' => $plan->name,
                'description' => $plan->description,
                'price' => number_format($plan->price, 2, ',', ' ') . ' PLN',
                'period' => $plan->period->value . ' ' . $periodDeclination,
                'features' => $features,
                'is_current' => $this->currentSubscription?->plan_id === $plan->id,
                'type' => $plan->type->label(),
            ];
        })->toArray();
    }

    protected function formatPlanFeatures(array $features): array
    {
        $formattedFeatures = [];

        foreach ($features as $feature) {
            if (is_numeric($feature)) {
                // Feature is a SystemModules enum value
                $module = SystemModules::tryFrom((int)$feature);
                if ($module) {
                    $formattedFeatures[] = $module->label();
                }
            } elseif (is_string($feature)) {
                // Feature is a custom string
                $formattedFeatures[] = $feature;
            }
        }

        return $formattedFeatures;
    }

    protected function shouldShowRenewalButton(): bool
    {
        return $this->currentSubscription &&
            $this->currentSubscription->status === SubscriptionStatus::ACTIVE &&
            $this->currentSubscription->plan->type === PlanType::PAID &&
            $this->pendingSubscription === null &&
            SubRepository::isSubscriptionNearExpiry($this->currentSubscription);
    }

    protected function shouldShowCancelButton(): bool
    {
        return $this->currentSubscription &&
            $this->currentSubscription->status === SubscriptionStatus::ACTIVE &&
            $this->currentSubscription->plan->type === PlanType::PAID;
    }

    protected function shouldShowContinuePaymentButton(): bool
    {
        return $this->currentSubscription &&
            $this->currentSubscription->status === SubscriptionStatus::NEW;
    }

    public function createNewSubscription(): Action
    {
        return Action::make('createNewSubscription')
            ->label('Wybierz plan')
            ->requiresConfirmation()
            ->modalHeading('Utwórz nową subskrypcję')
            ->modalDescription('Czy na pewno chcesz utworzyć nową subskrypcję?')
            ->modalSubmitActionLabel('Tak, utwórz')
            ->extraAttributes(['class' => 'w-full'])
            ->action(function (array $arguments) {
                $plan = Plan::findOrFail($arguments['planId']);
                $subRepo = new SubRepository();
                $subRepo->createSubscriptionWithPayment(auth()->user(), $plan);
                if ($subRepo->isPaymentFinished) {
                    tenant(true);
                    $this->redirect(SubscriptionManagement::getUrl());
                    return;
                }
                $this->redirectUrl = $subRepo->continuePaymentUrl;
                $this->redirect = true;
            })
            ->after(fn() => $this->redirect ? $this->redirect($this->redirectUrl) : true);
    }
}
