<?php

namespace App\Filament\Pages;

use App\Enums\DocumentTypes;
use App\Models\DTOBankData;
use App\Models\Partner;
use App\Models\Payment;
use App\Models\Tenant;
use App\Models\User;
use App\Repositories\DocumentSeriesRepository;
use App\Repositories\SystemSettingsRepository;
use App\Repositories\TradeDocsRepository;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Contracts\Support\Htmlable;

class SystemSettingsPage extends Page implements HasForms, HasActions
{
    use InteractsWithForms, InteractsWithActions;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';
    protected static ?string $navigationLabel = 'System Settings';
    protected static ?string $slug = 'system-settings';
    protected static string $view = 'filament.pages.system-settings';
//    protected static ?string $navigationGroup = 'System';
    protected static ?int $navigationSort = 100;

    public ?array $data = [];

    public bool $isSaved = false;

    protected SystemSettingsRepository $settingsRepository;

    public function mount(): void
    {
        $this->settingsRepository = new SystemSettingsRepository();
        $this->loadCurrentSettings();
        $this->form->fill($this->data);
        $this->isSaved = true;
    }

    public function getHeading(): string|Htmlable
    {
        return 'System Settings';
    }

    public function getSubheading(): string|Htmlable|null
    {
        return 'Configure system-wide settings and preferences';
    }

    protected function loadCurrentSettings(): void
    {
        $this->data = [
            'system_tenant_id' => $this->settingsRepository->getSystemTenantId(),
            'system_bank_account' => $this->settingsRepository->getSystemBankAccount(),
            'system_fvs_document_series' => $this->settingsRepository->getSystemFvsDocumentSeries(),
            'system_tenant_admin_user' => $this->settingsRepository->getSystemTenantAdminUser(),
        ];
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('System Tenant Configuration')
                    ->description('Configure the default tenant for system-wide operations')
                    ->schema([
                        Select::make('system_tenant_id')
                            ->label('System Tenant')
                            ->placeholder('Select a tenant...')
                            ->options($this->getTenantOptions())
                            ->searchable()
                            ->preload()
                            ->live()
                            ->afterStateUpdated(function ($state, Set $set) {
                                $this->updateTenantInfo($state);
                                $set('system_bank_account', null);
                                $set('system_fvs_document_series', null);
                                $set('system_tenant_admin_user', null);
                                $this->isSaved = false;
                            })
                            ->helperText(
                                'This tenant will be used for system-wide operations and default configurations'
                            )
                            ->columnSpanFull(),

                        Placeholder::make('tenant_info')
                            ->label('Selected Tenant Information')
                            ->content(function ($get) {
                                $tenantId = $get('system_tenant_id');
                                if (!$tenantId) {
                                    return 'No tenant selected';
                                }

                                $tenant = Tenant::find($tenantId);
                                if (!$tenant) {
                                    return 'Tenant not found';
                                }

                                return view('filament.components.tenant-info', ['tenant' => $tenant]);
                            })
                            ->visible(fn ($get) => !empty($get('system_tenant_id')))
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Section::make('Bank Account Configuration')
                    ->description('Select the default bank account for system operations')
                    ->schema([
                        Select::make('system_bank_account')
                            ->label('System Bank Account')
                            ->placeholder('Select a bank account...')
                            ->options(function (Get $get) {
                                return $this->getBankAccountOptions($get('system_tenant_id'));
                            })
                            ->live()
                            ->afterStateUpdated(function ($state, Set $set) {
                                $this->isSaved = false;
                            })
                            ->visible(fn (Get $get) => !empty($get('system_tenant_id')))
                            ->helperText('Bank account used for system-wide financial operations')
                            ->columnSpanFull(),
                    ])
                    ->visible(fn (Get $get) => !empty($get('system_tenant_id')))
                    ->columns(1),

                Section::make('Document Series Configuration')
                    ->description('Select the default FVS (Sales Invoice) document series')
                    ->schema([
                        Select::make('system_fvs_document_series')
                            ->label('FVS Document Series')
                            ->placeholder('Select a document series...')
                            ->options(function (Get $get) {
                                return $this->getFvsDocumentSeriesOptions($get('system_tenant_id'));
                            })
                            ->live()
                            ->afterStateUpdated(function ($state, Set $set) {
                                $this->isSaved = false;
                            })
                            ->visible(fn (Get $get) => !empty($get('system_tenant_id')))
                            ->helperText('Document series used for system-wide sales invoices (FVS)')
                            ->columnSpanFull(),
                    ])
                    ->visible(fn (Get $get) => !empty($get('system_tenant_id')))
                    ->columns(1),

                Section::make('Tenant Admin User Configuration')
                    ->description('Select the default tenant admin user for system operations')
                    ->schema([
                        Select::make('system_tenant_admin_user')
                            ->label('Tenant Admin User')
                            ->placeholder('Select an admin user...')
                            ->options(function (Get $get) {
                                return $this->getTenantAdminUserOptions($get('system_tenant_id'));
                            })
                            ->live()
                            ->afterStateUpdated(function ($state, Set $set) {
                                $this->isSaved = false;
                            })
                            ->visible(fn (Get $get) => !empty($get('system_tenant_id')))
                            ->helperText('Tenant admin user used for system-wide operations')
                            ->columnSpanFull(),
                    ])
                    ->visible(fn (Get $get) => !empty($get('system_tenant_id')))
                    ->columns(1),
            ])
            ->statePath('data');
    }

    protected function getTenantOptions(): array
    {
        return Tenant::active()
            ->orderBy('name')
            ->pluck('name', 'id')
            ->toArray();
    }

    protected function getBankAccountOptions(?int $tenantId): array
    {
        if (!$tenantId) {
            return [];
        }

        $tenant = Tenant::find($tenantId);
        if (!$tenant) {
            return [];
        }

        $bankAccounts = $tenant->getBankAccounts();
        if ($bankAccounts->isEmpty()) {
            return [];
        }

        $options = [];
        foreach ($bankAccounts as $index => $accountData) {
            $bankData = DTOBankData::make($accountData);
            $label = sprintf(
                '%s (%s) - %s %s',
                $bankData->account_name,
                $bankData->bank_name,
                $bankData->bank_account,
                $bankData->bank_currency
            );
            $options[$index] = $label;
        }

        return $options;
    }

    protected function getFvsDocumentSeriesOptions(?int $tenantId): array
    {
        if (!$tenantId) {
            return [];
        }

        $tenant = Tenant::find($tenantId);
        if (!$tenant) {
            return [];
        }

        $fvsSeries = DocumentSeriesRepository::getSeriesForTenant($tenant, [DocumentTypes::FVS->value]);

        if ($fvsSeries->isEmpty()) {
            return [];
        }

        $options = [];
        foreach ($fvsSeries as $series) {
            $label = sprintf('%s (%s)', $series->name, $series->pattern);
            $options[$series->id] = $label;
        }

        return $options;
    }

    protected function getTenantAdminUserOptions(?int $tenantId): array
    {
        if (!$tenantId) {
            return [];
        }

        $tenant = Tenant::find($tenantId);
        if (!$tenant) {
            return [];
        }

        $adminUsers = $tenant->admins()->with('profile')->get();

        if ($adminUsers->isEmpty()) {
            return [];
        }

        $options = [];
        foreach ($adminUsers as $user) {
            $fullName = $user->profile?->fullName() ?? 'No profile';
            $label = sprintf('%s (%s)', $fullName, $user->email);
            $options[$user->id] = $label;
        }

        return $options;
    }

    protected function updateTenantInfo($tenantId): void
    {
        // This method can be used to trigger UI updates when tenant selection changes
        // The actual display is handled by the Placeholder component
    }

    protected function getFormActions(): array
    {
        return [
            Action::make('save')
                ->label('Save Settings')
                ->action('save')
                ->color('primary'),

            Action::make('clearCache')
                ->label('Clear Cache')
                ->action('clearCache')
                ->color('gray')
                ->outlined(),

            Action::make('convertTenantsToPartnersAction')
                ->label('Convert Tenants to Partners')
                ->action('convertTenantsToPartners')
                ->color('gray')
                ->disabled(fn() => !$this->isSaved)
                ->outlined(),
        ];
    }

    public function save(): void
    {
        $data = $this->form->getState();

        try {
            $data = $this->validateSettings($data);
            $ssr = new SystemSettingsRepository();

            if (isset($data['system_tenant_id']) && $data['system_tenant_id']) {
                $ssr->setSystemTenantId($data['system_tenant_id']);
            }

            if (isset($data['system_bank_account']) && $data['system_bank_account'] !== null) {
                $ssr->setSystemBankAccount($data['system_bank_account']);
            }

            if (isset($data['system_fvs_document_series']) && $data['system_fvs_document_series']) {
                $ssr->setSystemFvsDocumentSeries($data['system_fvs_document_series']);
            }

            if (isset($data['system_tenant_admin_user']) && $data['system_tenant_admin_user']) {
                $ssr->setSystemTenantAdminUser($data['system_tenant_admin_user']);
            }

            Notification::make()
                ->title('Settings saved successfully')
                ->success()
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Error saving settings')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
        $this->isSaved = true;
    }

    protected function validateSettings(array $data): array
    {
        $tenantId = $data['system_tenant_id'] ?? null;

        if (!$tenantId) {
            throw new \Exception('Selected tenant not found');
        }

        $tenant = Tenant::find($tenantId);
        if (!$tenant) {
            throw new \Exception('Selected tenant not found');
        }

        // Validate bank account belongs to tenant
        if (filled($data['system_bank_account'])) {
            $bankAccounts = $tenant->getBankAccounts();
            if (!$bankAccounts->has($data['system_bank_account'])) {
                throw new \Exception('Selected bank account does not belong to the chosen tenant');
            }
        }

        // Validate document series belongs to tenant
        if (filled($data['system_fvs_document_series'])) {
            $series = DocumentSeriesRepository::getSeriesForTenant($tenant, [DocumentTypes::FVS->value]);
            if ($series->where('id', $data['system_fvs_document_series'])->count() === 0) {
                throw new \Exception('Selected document series does not belong to the chosen tenant');
            }
        }

        // Validate admin user belongs to tenant
        if (filled($data['system_tenant_admin_user'])) {
            $adminUsers = $tenant->admins();
            if (!$adminUsers->where('users.id', $data['system_tenant_admin_user'])->exists()) {
                throw new \Exception('Selected admin user does not belong to the chosen tenant');
            }
        }

        return $data;
    }

    public function convertTenantsToPartners()
    {
        $systemTenant = systemTenant();
        $existingPartners = Partner::query()->where('installation', $systemTenant->id)->get()->pluck('id')->toArray();
        $tenants = Tenant::query()->whereNot('id', $systemTenant->id)->get();
        $found = count($tenants);
        $converted = 0;
        foreach ($tenants as $tenant) {
            if (in_array($tenant->system_partner, $existingPartners)) {
                continue;
            }
            $partner = $tenant->convertToPartner($systemTenant->id);
            $partner->save();
            $tenant->update(['system_partner' => $partner->id]);
            $converted++;
        }

        Notification::make()
            ->title('Tenants converted to partners')
            ->body("Found: {$found}, Converted: {$converted}")
            ->success()
            ->send();
        return true;
    }

    public function clearCache(): void
    {
        try {
            $this->settingsRepository->clearCache();

            Notification::make()
                ->title('Cache cleared successfully')
                ->success()
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Error clearing cache')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    public static function canAccess(): bool
    {
        // Only super admins can access system settings
        return auth()->user()?->isGod() ?? false;
    }
}
