<?php

namespace App\Filament\Resources\SubscriptionResource\RelationManagers;

use App\Enums\PlanType;
use App\Services\Payments\InternalPaymentProvider;
use App\Services\Payments\PayUPaymentProvider;
use Filament\Actions\Action;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class PaymentsRelationManager extends RelationManager
{
    protected static string $relationship = 'payments';

    protected $paymentUrl = '';


    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('hash')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('meta.status.statusCode')
                    ->visible(fn($state) => !blank($state))
                    ->label('Status'),
                Forms\Components\TextInput::make('meta.error')
                    ->visible(fn($state) => !blank($state))
                    ->label('Błąd'),
                Forms\Components\TextInput::make('meta.redirectUri')
                    ->columnSpanFull()
                    ->label('Link'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('hash')
            ->columns([
                Tables\Columns\TextColumn::make('hash'),
                Tables\Columns\TextColumn::make('provider')
                ->formatStateUsing(fn($state) => (new $state())->getName()),
                Tables\Columns\TextColumn::make('provider_payment_id'),
                Tables\Columns\TextColumn::make('amount')
                ->formatStateUsing(fn($state) => number_format($state / 100, 2, ',', ' ')),
                Tables\Columns\TextColumn::make('status')
                ->formatStateUsing(fn($state) => $state->name),
                Tables\Columns\TextColumn::make('paid_at')
                ->dateTime(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\Action::make('create')
                    ->label('Dodaj płatność')
                    ->modal()
                    ->modalDescription('Utwórz nową płatność')
                    ->action(function () {
                        $parent = $this->getOwnerRecord();
                        if ($parent->plan->type === PlanType::TRIAL) {
                            Notification::make()->title('Płatność lokalna')
                                ->warning()
                                ->send();
                            $paymentProvider = new InternalPaymentProvider();
                        } else {
                            $paymentProvider = new PayUPaymentProvider();
                        }
                        $result = $paymentProvider->charge($parent->user, $parent, ['currency' => 'PLN']);
                        $payment = $paymentProvider->payment;
                        if (blank($result)) {
                            Notification::make()->title('Błąd podczas tworzenia płatności')
                                ->body('Wystąpił błąd: ' . $payment->meta['error'] ?? 'Nieznany błąd')
                                ->danger()
                                ->send();
                            return false;
                        }

                        if ($result === true) {
                            return true;
                        }

                        $this->paymentUrl = $result;
                        Notification::make()
                            ->title('Płatność została utworzona')
                            ->persistent()
                            ->body('Kliknij ten link: ')
                            ->actions([
                                \Filament\Notifications\Actions\Action::make('view')
                                    ->label('Przejdz do płatności')
                                    ->button()
                                    ->url($this->paymentUrl, shouldOpenInNewTab: true),
                            ])
                            ->success()
                            ->send();
                        return true;
                    })
                    ->sendSuccessNotification()
                    ->icon('heroicon-o-plus'),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                ->hiddenLabel(),
                Tables\Actions\DeleteAction::make()
                ->hiddenLabel(),
            ])
            ->bulkActions([
//                Tables\Actions\BulkActionGroup::make([
//                    Tables\Actions\DeleteBulkAction::make(),
//                ]),
            ]);
    }
}
