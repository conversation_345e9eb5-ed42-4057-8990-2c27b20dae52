<?php

namespace App\Filament\Resources\TenantResource\RelationManagers;

use App\Enums\SubscriptionStatus;
use App\Enums\WarehouseTypes;
use App\Models\User;
use App\Models\Warehouse;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SubscriptionsRelationManager extends RelationManager
{
    protected static string $relationship = 'subscriptions';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->options(function (Forms\Get $get) {
                        if (blank($get('tenant_id'))) {
                            return [];
                        }
                        return User::query()
                            ->whereHas('tenant', fn($q) => $q->where('tenant_id', $get('tenant_id')))
                            ->get()
                            ->pluck('email', 'id');
                    })
                    ->label('User')
                    ->searchable()
                    ->preload()
                    ->required(),
                Forms\Components\Select::make('plan_id')
                    ->relationship('plan', 'name')
                    ->label('Plan')
                    ->preload()
                    ->searchable()
                    ->required(),
                Forms\Components\Select::make('status')
                    ->options(SubscriptionStatus::class)
                    ->required(),
                Forms\Components\DatePicker::make('trial_ends_at'),
                Forms\Components\DatePicker::make('ends_at')
                    ->required(),
            ]);
    }


    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('plan.name'),
                Tables\Columns\TextColumn::make('status')->formatStateUsing(fn($state) => $state->label() ?? 'Unknown'),
                Tables\Columns\TextColumn::make('starts_at')
                    ->date(),
                Tables\Columns\TextColumn::make('trial_ends_at')
                    ->toggleable(true, true)
                    ->date(),
                Tables\Columns\TextColumn::make('ends_at')
                    ->date(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
//                Tables\Actions\CreateAction::make()
//                    ->mutateFormDataUsing(function (array $data) {
//                        $data['is_active'] = 1;
//                        return $data;
//                    })
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
//                Tables\Actions\DeleteAction::make(),
            ]);
    }
}
