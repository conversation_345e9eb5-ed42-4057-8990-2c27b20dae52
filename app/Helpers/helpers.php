<?php

if (! function_exists('tenant')) {
    function tenant(bool $refresh = false): ?\App\Models\Tenant
    {
        if (null === auth()->user()) {
            return null;
        }

        if ($refresh || !session()->has(auth()->user()->id . '_tenant')) {
            $tenant = auth()->user()->getTenant();
            if (null === $tenant) {
                return null;
            }

            session()->put(auth()->user()->id . '_tenant', $tenant);
        }

        return session()->get(auth()->user()->id . '_tenant', null);
    }
}

if (! function_exists('tenantFlush')) {
    function tenantFlush(): void
    {
        if (null === auth()->getUser()) {
            return;
        }

        session()->forget(auth()->user()->id . '_tenant');
    }
}

if (! function_exists('appVersion')) {
    function appVersion(bool $refresh = false): array
    {
        [$major, $minor, $patch] = explode('.', config('app.version'));
        return ['major' => $major, 'minor' => $minor, 'patch' => $patch];
    }
}


if (! function_exists('systemSetting')) {
    function systemSetting(string $key, $default = null)
    {
        static $repository = null;

        if ($repository === null) {
            $repository = new \App\Repositories\SystemSettingsRepository();
        }

        return $repository->get($key, $default);
    }
}

if (! function_exists('setSystemSetting')) {
    function setSystemSetting(string $key, $value, ?string $description = null): \App\Models\SystemSetting
    {
        static $repository = null;

        if ($repository === null) {
            $repository = new \App\Repositories\SystemSettingsRepository();
        }

        return $repository->set($key, $value, $description);
    }
}

if (! function_exists('systemTenant')) {
    function systemTenant(): ?\App\Models\Tenant
    {
        static $repository = null;

        if ($repository === null) {
            $repository = new \App\Repositories\SystemSettingsRepository();
        }

        $tenantId = $repository->getSystemTenantId();

        if (!$tenantId) {
            return null;
        }

        return \App\Models\Tenant::find($tenantId);
    }
}

if (! function_exists('systemBankAccount')) {
    function systemBankAccount(): ?\App\Models\DTOBankData
    {
        static $repository = null;

        if ($repository === null) {
            $repository = new \App\Repositories\SystemSettingsRepository();
        }

        $tenant = systemTenant();
        $bankAccountIndex = $repository->getSystemBankAccount();

        if (!$tenant || $bankAccountIndex === null) {
            return null;
        }

        $bankAccounts = $tenant->getBankAccounts();
        $accountData = $bankAccounts->get($bankAccountIndex);

        return $accountData ? \App\Models\DTOBankData::make($accountData) : null;
    }
}

if (! function_exists('systemFvsDocumentSeries')) {
    function systemFvsDocumentSeries(): ?\App\Models\DocumentSeriesPattern
    {
        static $repository = null;

        if ($repository === null) {
            $repository = new \App\Repositories\SystemSettingsRepository();
        }

        $seriesId = $repository->getSystemFvsDocumentSeries();

        if (!$seriesId) {
            return null;
        }

        return \App\Models\DocumentSeriesPattern::find($seriesId);
    }
}

if (! function_exists('systemTenantAdminUser')) {
    function systemTenantAdminUser(): ?\App\Models\User
    {
        static $repository = null;

        if ($repository === null) {
            $repository = new \App\Repositories\SystemSettingsRepository();
        }

        $userId = $repository->getSystemTenantAdminUser();

        if (!$userId) {
            return null;
        }

        return \App\Models\User::with('profile')->find($userId);
    }
}

function imageFileToBase64(string $path): string
{

    $imageData = \Illuminate\Support\Facades\File::get($path);
    if (empty($imageData)) {
        return '';
    }

    $mimeType = \Illuminate\Support\Facades\File::mimeType($path);

    if (empty($mimeType)) {
        trigger_error("Nie można rozpoznać typu MIME dla pliku", E_USER_WARNING);
        return '';
    }

    $base64 = base64_encode($imageData);
    if ($base64 === false) {
        trigger_error("Nie można zakodować danych obrazu w Base64.", E_USER_WARNING);
        return '';
    }

    return 'data:' . $mimeType . ';base64,' . $base64;
}

/**
 * Get tenant logo as base64-encoded image data
 *
 * @param \App\Models\Tenant $tenant
 * @return string Base64-encoded image data for use in HTML img src tags, empty string if no logo
 */
function tenantLogo(\App\Models\Tenant $tenant): string
{
    try {
        // Get tenant metadata
        $metaData = $tenant->meta?->meta ?? [];
        if (empty($metaData)) {
            return '';
        }

        // Create DTO from metadata
        $metadataDto = \App\Models\DTOTenantMetadata::make($metaData);

        // Get logo image data
        $logoImage = $metadataDto->getImageByType('logo');
        if (!$logoImage) {
            return '';
        }

        $logoPath = storage_path("app/tenants/{$tenant->hash}/images/{$logoImage->filename}");

        if (!\Illuminate\Support\Facades\File::exists($logoPath)) {
            return '';
        }

        return imageFileToBase64($logoPath);
    } catch (\Exception $e) {
        \Illuminate\Support\Facades\Log::warning('Failed to load tenant logo: ' . $e->getMessage());
        return '';
    }
}
