<?php

namespace App\Console\Commands;

use App\Enums\SubscriptionStatus;
use App\Jobs\SubscriptionExpired;
use App\Models\Subscription;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CheckExpiredSubscriptions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscription:expired {--dry-run : Preview subscriptions without sending emails}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Mark expired subscriptions and send email notifications to users
    whose subscriptions end tomorrow';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $isDryRun = $this->option('dry-run');
        $targetDate = Carbon::now()->format('Y-m-d');

        $this->info("Looking for subscriptions ending on: {$targetDate}");

        if ($isDryRun) {
            $this->warn('DRY RUN MODE - No emails will be sent');
        }

        // Query subscriptions ending in exactly 1 days
        $subscriptions = Subscription::with(['user', 'plan'])
            ->where('status', SubscriptionStatus::ACTIVE->name)
            ->whereDate('ends_at', '<', $targetDate)
            ->whereNull('ended_at')
            ->get();

        $totalFound = $subscriptions->count();
        $this->info("Found {$totalFound} subscription(s) expired");

        if ($totalFound === 0) {
            $this->info('No subscriptions found. Exiting.');
            return self::SUCCESS;
        }

        // Display preview information
        if ($isDryRun || $this->option('verbose')) {
            $this->displaySubscriptionPreview($subscriptions);
        }

        if ($isDryRun) {
            $this->info('Dry run completed. No emails were sent.');
            return self::SUCCESS;
        }

        foreach ($subscriptions as $subscription) {
            SubscriptionExpired::dispatch($subscription);
        }
        $this->displayResults($totalFound);
        return self::SUCCESS;
    }

    /**
     * Display preview of subscriptions that would be notified
     */
    private function displaySubscriptionPreview($subscriptions): void
    {
        $this->newLine();
        $this->info('Subscriptions to be notified:');

        $headers = ['ID', 'User Email', 'Plan', 'Ends At', 'Price'];
        $rows = [];

        foreach ($subscriptions as $subscription) {
            $rows[] = [
                $subscription->id,
                $subscription->user->email ?? 'N/A',
                $subscription->plan->name ?? 'N/A',
                $subscription->ends_at->format('Y-m-d'),
                $this->formatPrice($subscription->plan->price ?? 0)
            ];
        }

        $this->table($headers, $rows);
        $this->newLine();
    }


    /**
     * Display final results
     */
    private function displayResults(int $totalFound): void
    {
        $this->info("📊 Total processed: {$totalFound}");
    }

    /**
     * Format price for display
     */
    private function formatPrice($price): string
    {
        if (is_object($price) && method_exists($price, '__toString')) {
            return (string) $price;
        }

        // Fallback formatting if price is numeric
        if (is_numeric($price)) {
            return number_format($price, 2, ',', ' ') . ' zł';
        }

        return 'N/A';
    }
}
