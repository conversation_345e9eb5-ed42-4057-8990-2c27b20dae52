<?php

namespace App\Console\Commands;

use App\Models\Tenant;
use App\Repositories\SystemSettingsRepository;
use Illuminate\Console\Command;

class SystemSettingsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'system:settings
                            {action : The action to perform (get|set|list|clear-cache)}
                            {key? : The setting key}
                            {value? : The setting value}
                            {--description= : Description for the setting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage system settings';

    private SystemSettingsRepository $repository;

    public function __construct()
    {
        parent::__construct();
        $this->repository = new SystemSettingsRepository();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        match ($action) {
            'get' => $this->getSetting(),
            'set' => $this->setSetting(),
            'list' => $this->listSettings(),
            'clear-cache' => $this->clearCache(),
            default => $this->error("Unknown action: {$action}"),
        };
    }

    private function getSetting(): void
    {
        $key = $this->argument('key');
        if (!$key) {
            $this->error('Key is required for get action');
            return;
        }

        $value = $this->repository->get($key);
        if ($value === null) {
            $this->warn("Setting '{$key}' not found");
        } else {
            $this->info("Setting '{$key}': " . json_encode($value));
        }
    }

    private function setSetting(): void
    {
        $key = $this->argument('key');
        $value = $this->argument('value');

        if (!$key || $value === null) {
            $this->error('Both key and value are required for set action');
            return;
        }

        $description = $this->option('description');

        // Try to decode JSON value
        $decodedValue = json_decode($value, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            $value = $decodedValue;
        }

        $setting = $this->repository->set($key, $value, $description);
        $this->info("Setting '{$key}' saved successfully");
    }

    private function listSettings(): void
    {
        $settings = $this->repository->all();

        if ($settings->isEmpty()) {
            $this->info('No settings found');
            return;
        }

        $this->table(
            ['Key', 'Value', 'Description', 'Updated At'],
            $settings->map(function ($setting, $key) {
                return [
                    $key,
                    json_encode($setting['value']),
                    $setting['description'] ?? 'N/A',
                    $setting['updated_at']?->format('Y-m-d H:i:s') ?? 'N/A',
                ];
            })->toArray()
        );

        // Show helper information for system settings
        $this->newLine();
        $this->info('System Configuration Summary:');

        $systemTenant = systemTenant();
        $this->line('System Tenant: ' . ($systemTenant ? $systemTenant->name : 'Not configured'));

        $systemBankAccount = systemBankAccount();
        $this->line('System Bank Account: ' . ($systemBankAccount ? $systemBankAccount->account_name : 'Not configured'));

        $systemFvsSeries = systemFvsDocumentSeries();
        $this->line('System FVS Document Series: ' . ($systemFvsSeries ? $systemFvsSeries->name : 'Not configured'));

        $systemAdminUser = systemTenantAdminUser();
        $this->line('System Admin User: ' . ($systemAdminUser ? $systemAdminUser->profile?->fullName() . ' (' . $systemAdminUser->email . ')' : 'Not configured'));
    }

    private function clearCache(): void
    {
        $this->repository->clearCache();
        $this->info('System settings cache cleared successfully');
    }
}
