<?php

namespace App\Console;

use App\Enums\SubscriptionStatus;
use App\Models\Subscription;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        $schedule->command('wh:import-drugs')
            ->timezone(new \DateTimeZone('Europe/Warsaw'))
            ->dailyAt('06:00');
        $schedule->command('wh:daily-snapshot --all')
            ->timezone(new \DateTimeZone('Europe/Warsaw'))
            ->dailyAt('23:30');
        $schedule->command('wh:clean-product-demands --no-confirm')
            ->timezone(new \DateTimeZone('Europe/Warsaw'))
            ->dailyAt('23:35');
        $schedule->command('wh:update-currency-ratio')
            ->timezone(new \DateTimeZone('Europe/Warsaw'))
            ->dailyAt('12:30')
            ->dailyAt('18:30');
        $schedule->call(function () {
            Subscription::where('ends_at', '<=', now())
                ->where('status', SubscriptionStatus::ACTIVE)
                ->update(['status' => SubscriptionStatus::EXPIRED]);
        })->daily();

        $schedule->command('subscription:notify-ending-7d')
            ->timezone(new \DateTimeZone('Europe/Warsaw'))
            ->dailyAt('06:00');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
