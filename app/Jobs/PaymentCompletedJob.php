<?php

namespace App\Jobs;

use App\Enums\PlanType;
use App\Enums\SubscriptionStatus;
use App\Events\PaymentCompleted;
use App\Models\Payment;
use App\Repositories\TradeDocsRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class PaymentCompletedJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(public Payment $payment)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $this->payment->subscription->paymentSuccessful();

        if ($this->payment->subscription->status === SubscriptionStatus::ACTIVE) {
            $this->payment->tenant->activateSubscription($this->payment->subscription);
        }

        if ($this->payment->subscription->plan->type === PlanType::PAID) {
            $invoice = TradeDocsRepository::createSubscriptionInvoice($this->payment);
            $this->payment->subscription->update(['invoice_id' => $invoice->getKey()]);
        }

        PaymentCompleted::dispatch($this->payment);
    }
}
