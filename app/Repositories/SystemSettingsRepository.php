<?php

namespace App\Repositories;

use App\Models\SystemSetting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Collection;

class SystemSettingsRepository
{
    /**
     * Cache TTL in seconds (24 hours)
     */
    private const CACHE_TTL = 86400;

    /**
     * Cache key prefix
     */
    private const CACHE_PREFIX = 'system_settings';

    /**
     * Get a setting value with cache-first approach
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function get(string $key, $default = null)
    {
        $cacheKey = $this->getCacheKey($key);
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($key, $default) {
            $setting = SystemSetting::where('key', $key)->first();
            return $setting ? $setting->value : $default;
        });
    }

    /**
     * Set a setting value and update cache
     *
     * @param string $key
     * @param mixed $value
     * @param string|null $description
     * @return SystemSetting
     */
    public function set(string $key, $value, ?string $description = null): SystemSetting
    {
        $setting = SystemSetting::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'description' => $description,
            ]
        );

        // Update cache
        $cacheKey = $this->getCacheKey($key);
        Cache::put($cacheKey, $value, self::CACHE_TTL);

        // Invalidate all settings cache
        $this->forgetAllCache();

        return $setting;
    }

    /**
     * Remove a setting from database and cache
     *
     * @param string $key
     * @return bool
     */
    public function forget(string $key): bool
    {
        $deleted = SystemSetting::where('key', $key)->delete() > 0;

        if ($deleted) {
            // Remove from cache
            $cacheKey = $this->getCacheKey($key);
            Cache::forget($cacheKey);

            // Also remove exists cache
            $existsCacheKey = $this->getCacheKey($key . '_exists');
            Cache::forget($existsCacheKey);

            // Invalidate all settings cache
            $this->forgetAllCache();
        }

        return $deleted;
    }

    /**
     * Get all settings with caching
     *
     * @return Collection
     */
    public function all(): Collection
    {
        $cacheKey = $this->getCacheKey('all');
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () {
            return SystemSetting::all()->keyBy('key')->map(function ($setting) {
                return [
                    'value' => $setting->value,
                    'description' => $setting->description,
                    'updated_at' => $setting->updated_at,
                ];
            });
        });
    }

    /**
     * Check if a setting exists
     *
     * @param string $key
     * @return bool
     */
    public function exists(string $key): bool
    {
        $cacheKey = $this->getCacheKey($key . '_exists');
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($key) {
            return SystemSetting::where('key', $key)->exists();
        });
    }

    /**
     * Get system tenant ID
     *
     * @return int|null
     */
    public function getSystemTenantId(): ?int
    {
        $tenantId = $this->get('system_tenant_id');
        return $tenantId ? (int) $tenantId : null;
    }

    /**
     * Set system tenant ID
     *
     * @param int $tenantId
     * @return SystemSetting
     */
    public function setSystemTenantId(int $tenantId): SystemSetting
    {
        return $this->set(
            'system_tenant_id',
            $tenantId,
            'ID of the system tenant used for system-wide operations'
        );
    }

    /**
     * Get system bank account identifier
     *
     * @return string|null
     */
    public function getSystemBankAccount(): ?string
    {
        return $this->get('system_bank_account');
    }

    /**
     * Set system bank account identifier
     *
     * @param string $bankAccountIdentifier
     * @return SystemSetting
     */
    public function setSystemBankAccount(string $bankAccountIdentifier): SystemSetting
    {
        return $this->set(
            'system_bank_account',
            $bankAccountIdentifier,
            'Identifier of the system bank account used for system-wide operations'
        );
    }

    /**
     * Get system FVS document series ID
     *
     * @return int|null
     */
    public function getSystemFvsDocumentSeries(): ?int
    {
        $seriesId = $this->get('system_fvs_document_series');
        return $seriesId ? (int) $seriesId : null;
    }

    /**
     * Set system FVS document series ID
     *
     * @param int $seriesId
     * @return SystemSetting
     */
    public function setSystemFvsDocumentSeries(int $seriesId): SystemSetting
    {
        return $this->set(
            'system_fvs_document_series',
            $seriesId,
            'ID of the FVS document series used for system-wide sales invoices'
        );
    }

    /**
     * Get system tenant admin user ID
     *
     * @return int|null
     */
    public function getSystemTenantAdminUser(): ?int
    {
        $userId = $this->get('system_tenant_admin_user');
        return $userId ? (int) $userId : null;
    }

    /**
     * Set system tenant admin user ID
     *
     * @param int $userId
     * @return SystemSetting
     */
    public function setSystemTenantAdminUser(int $userId): SystemSetting
    {
        return $this->set(
            'system_tenant_admin_user',
            $userId,
            'ID of the tenant admin user used for system-wide operations'
        );
    }

    /**
     * Clear all settings cache
     *
     * @return void
     */
    public function clearCache(): void
    {
        $this->forgetAllCache();
    }

    /**
     * Generate cache key for a setting
     *
     * @param string $key
     * @return string
     */
    private function getCacheKey(string $key): string
    {
        return self::CACHE_PREFIX . '.' . $key;
    }

    /**
     * Forget all cached settings
     *
     * @return void
     */
    private function forgetAllCache(): void
    {
        Cache::forget($this->getCacheKey('all'));
        
        // You could also implement cache tagging here if using Redis/Memcached
        // Cache::tags([self::CACHE_PREFIX])->flush();
    }
}
