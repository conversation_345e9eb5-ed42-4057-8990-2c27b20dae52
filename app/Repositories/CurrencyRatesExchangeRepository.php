<?php

namespace App\Repositories;

use App\Enums\CurrencyExchangeRatesProviders;
use App\Models\CurrencyRate;
use App\Services\CurrencyRatesExchange\CurrencyRateResponse;
use App\Services\CurrencyRatesExchange\RatesExchangeProviderClass;
use DateTimeInterface;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class CurrencyRatesExchangeRepository
{
    public static string $storage = 'file';
    public static string $path = 'currency/';


    public static function updateExchangeRates(
        string $baseCurrencyIsoCode,
        string|\DateTimeInterface $date
    ): ?CurrencyRateResponse {
        $service = self::resolveCurrencyRatesProvider($baseCurrencyIsoCode);
        Log::debug('Fetching exchange rates for: ' . $baseCurrencyIsoCode);
        $resp = $service->getRates($date);
        if (null === $resp) {
            Log::debug('Fetching exchange rates for: ' . $baseCurrencyIsoCode . ' failed');
            return null;
        }
        self::putToStorage($resp);
        return $resp;
    }

    public static function getRate(
        string $baseCurrency,
        string $counterCurrency,
        string|\DateTimeInterface $date
    ): ?float {
        if (is_string($date)) {
            $date = new Carbon($date);
        }
        $data = self::getFromStorage(
            CurrencyExchangeRatesProviders::tryFrom($baseCurrency)->name,
            $baseCurrency,
            $counterCurrency,
            $date
        );
        return $data;
    }

    public static function getNearestRate(
        string $baseCurrency,
        string $counterCurrency,
        string|\DateTimeInterface $date
    ): array {
        if (is_string($date)) {
            $date = new Carbon($date);
        }
        $limit = new Carbon($date);
        $limit->subDays(8);
        do {
            $rate = self::getFromStorage(
                CurrencyExchangeRatesProviders::tryFrom($baseCurrency)->name,
                $baseCurrency,
                $counterCurrency,
                $date
            );
            if (($date < $limit) || filled($rate)) {
                break;
            }
            $date->subDay();
        } while (true);
        return [$rate, $date->format('Y-m-d')];
    }

    protected static function getFromStorage(string $provider, $baseCurrency, $destCurrency, $date)
    {
        switch (self::$storage) {
            default:
            case 'file':
                $name = self::resolveStorageFilePath($provider, $baseCurrency, $date);
                $path = self::$path . $name;
                $content = Storage::json($path);
                return $content['rates'][$destCurrency] ?? null;
            case 'mysql':
                return null;
        }
    }


    protected static function putToStorage(CurrencyRateResponse $data)
    {
        switch (self::$storage) {
            default:
            case 'file':
                $name = self::resolveStorageFilePath($data->provider, $data->baseCurrencyCode, $data->effectiveDate);
                $dir = self::resolveStoragePath($data->provider, $data->effectiveDate);
                $path = self::$path . $name;
                Storage::disk('local')->put($path, json_encode($data, JSON_PRETTY_PRINT), 'public');
//                chmod(Storage::disk('local')->path(self::$path . $dir), 0775);
                break;
            case 'mysql':
                foreach ($data->rates as $rate) {
                    $model = new CurrencyRate();
                }
                $name = $data->effectiveDate->format('Ymd') . '_' . $data->provider . $data->baseCurrencyCode . '.json';
                $path = Storage::path(self::$path . $name);
                Storage::put($path, json_encode($data, JSON_PRETTY_PRINT));
                break;
        }
    }

    public static function resolveStorageFilePath(
        string $provider,
        string $baseCurrencyCode,
        DateTimeInterface $date
    ): string {
        return $provider . '/' .
            $date->format('Y') . '/' .
            $date->format('md') . '_' . $baseCurrencyCode . '.json';
    }

    public static function resolveStoragePath(
        string $provider,
        DateTimeInterface $date
    ): string {
        return $provider . '/' .
            $date->format('Y');
    }

    public static function resolveCurrencyRatesProvider(string $baseCurrencyIsoCode): RatesExchangeProviderClass
    {
        $provider = CurrencyExchangeRatesProviders::tryFrom($baseCurrencyIsoCode) ??
            CurrencyExchangeRatesProviders::SWAP;
        $classname = '\\App\\Services\\CurrencyRatesExchange\\' . $provider->name;
        return app($classname);
    }
}
