<?php

namespace App\Repositories;

use App\Enums\PlanType;
use App\Enums\SubscriptionStatus;
use App\Events\SubscriptionCanceled;
use App\Filament\App\Pages\SubscriptionManagement;
use App\Mail\SubscriptionEnded;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\User;
use App\Services\Payments\InternalPaymentProvider;
use App\Services\Payments\PayUPaymentProvider;
use Filament\Notifications\Notification;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Mail;

class SubscriptionsRepository
{

    public Subscription $subscription;
    public Plan $plan;
    public bool $isPaymentFinished = false;
    public ?string $continuePaymentUrl = null;


    public static function getActiveSubscription(User $user): ?Subscription
    {
        return Subscription::where('user_id', $user->id)
            ->where('tenant_id', $user->installation())
            ->where('status', SubscriptionStatus::ACTIVE->name)
            ->first();
    }

    public function createSubscription(User $user, Plan $plan, array $options = []): Subscription
    {
        return Subscription::create([
            'user_id' => $user->id,
            'tenant_id' => $user->installation(),
            'plan_id' => $plan->id,
            'status' => SubscriptionStatus::NEW,
            'price' => $plan->price,
            'starts_at' => $options['starts_at'] ?? now(),
            'ends_at' => $options['ends_at'] ?? now()->addMonths($plan->period->value)->endOfDay(),
        ]);
    }

    public function createSubscriptionWithPayment(User $user, Plan $plan, array $options = []): Subscription
    {
        $subscription = $this->createSubscription($user, $plan, $options);
        if ($plan->type === PlanType::TRIAL) {
            $paymentProvider = new InternalPaymentProvider();
            $paymentProvider->charge($user, $subscription, ['currency' => 'PLN']);
            $this->isPaymentFinished = true;
            $subscription->paymentSuccessful();
            $subscription->tenant->activateSubscription($subscription);
            tenant(true);
            return $subscription;
        }

        $paymentProvider = new PayUPaymentProvider();
        $this->continuePaymentUrl = $paymentProvider->charge($user, $subscription, ['currency' => 'PLN']);
        return $subscription;
    }


    public function renewSubscriptionWithPayment(
        User $user,
        Plan $plan,
        Subscription $oldSubscription,
        array $options = []
    ): Subscription {
        if ($plan->type === PlanType::TRIAL) {
            throw new \InvalidArgumentException('Cannot renew trial subscription');
        }

        $options['starts_at'] = self::calculateRenewalStartDate($plan, $oldSubscription);
        $options['ends_at'] = Carbon::parse($oldSubscription->ends_at)
            ->addDay()
            ->addMonths($plan->period->value)
            ->endOfDay();
        $subscription = $this->createSubscription($user, $plan, $options);

        $paymentProvider = new PayUPaymentProvider();
        $this->continuePaymentUrl = $paymentProvider->charge($user, $subscription, ['currency' => 'PLN']);
        return $subscription;
    }

    public function cancelSubscription(Subscription $subscription): bool
    {
        if ($subscription->status === SubscriptionStatus::CANCELED) {
            return true;
        }

        $subscription->tenant->cancelSubscription();

        $paymentProvider = new PayUPaymentProvider();
        $paymentProvider->cancelSubscription($subscription);

        SubscriptionCanceled::dispatch($subscription);
        return true;
    }

    public function expireSubscription(Subscription $subscription): bool
    {
        if ($subscription->status === SubscriptionStatus::EXPIRED ||
            $subscription->status === SubscriptionStatus::CANCELED) {
            return true;
        }

        $subscription->tenant->expireSubscription();

        $paymentProvider = new PayUPaymentProvider();
        $paymentProvider->cancelSubscription($subscription);

        Mail::to($subscription->user->email)
            ->queue(new SubscriptionEnded($subscription));

        Notification::make()
            ->title('Twoja subskrypcja wygasła!')
            ->body('Wybierz nową subskrypcję aby kontynuować korzystanie z naszych usług.')
            ->actions([
                \Filament\Notifications\Actions\Action::make('view')
                    ->label('Przejdz do panelu subskrypcji')
                    ->button()
                    ->url(SubscriptionManagement::getUrl(), shouldOpenInNewTab: false),
            ])
            ->warning()
            ->sendToDatabase($subscription->user);
        return true;
    }

    public function swapSubscription(Subscription $subscription): bool
    {
        if ($subscription->status === SubscriptionStatus::EXPIRED ||
            $subscription->status === SubscriptionStatus::CANCELED) {
            return true;
        }

        $subscription->tenant->expireSubscription();

        $paymentProvider = new PayUPaymentProvider();
        $paymentProvider->cancelSubscription($subscription);

        $pendingSubscription = $subscription->tenant->getPendingSubscription();
        if ($pendingSubscription) {
            $pendingSubscription->tenant->activateSubscription($pendingSubscription);
            $pendingSubscription->status = SubscriptionStatus::ACTIVE;
            $pendingSubscription->save();
            return true;
        }

        Mail::to($subscription->user->email)
            ->queue(new SubscriptionEnded($subscription));

        Notification::make()
            ->title('Twoja subskrypcja wygasła!')
            ->body('Wybierz nową subskrypcję aby kontynuować korzystanie z naszych usług.')
            ->actions([
                \Filament\Notifications\Actions\Action::make('view')
                    ->label('Przejdz do panelu subskrypcji')
                    ->button()
                    ->url(SubscriptionManagement::getUrl(), shouldOpenInNewTab: false),
            ])
            ->warning()
            ->sendToDatabase($subscription->user);
        return true;
    }

    public static function isSamePlan(Plan $newPlan, Subscription $currentSubscription): bool
    {
        $currentPlan = $currentSubscription->plan;

        $currentFeatures = $currentPlan->features ?? [];
        $newFeatures = $newPlan->features ?? [];
        sort($currentFeatures);
        sort($newFeatures);

        return $currentPlan->period === $newPlan->period &&
            $currentPlan->type === $newPlan->type &&
            $currentFeatures === $newFeatures;
    }

    public static function calculateRenewalStartDate(Plan $selectedPlan, Subscription $currentSubscription): Carbon
    {
        if (self::isSamePlan($selectedPlan, $currentSubscription)) {
            return Carbon::today();
        }
        return Carbon::parse($currentSubscription->ends_at)->addDay();
    }

    public static function isSubscriptionNearExpiry(Subscription $subscription): bool
    {
        return Carbon::parse($subscription->ends_at)->diffInDays(Carbon::today()) <= 7;
    }
}
