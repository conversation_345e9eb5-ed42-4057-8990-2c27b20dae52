<?php

namespace App\Repositories;

use App\Helpers\Identifiers;
use App\Models\Partner;
use App\Models\PartnerWork;
use Illuminate\Support\Collection;

class PartnersRepository
{
    public static $error;

    public static function createFromModalForm($data): null|Partner
    {
        $data = self::mutateFormDataBeforeCreate($data);
        $partner = new Partner($data);
        try {
            $partner->save();
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return null;
        }
        return $partner;
    }

    public static function mutateFormDataBeforeCreate(array $data): array
    {
        $data['installation'] = auth()->user()?->installation() ?? 0;
        $data['hash'] = Identifiers::getRandomHash(24);
        return collect($data)->filter(fn($value) => !is_null($value))->toArray();
    }

    public static function getPartnerWorkshops(int|Partner $partner): array|Collection
    {
        if (is_int($partner)) {
            $partner = Partner::find($partner);
        }
        return $partner?->workshops()->get() ?? collect([]);
    }

    public static function getPartnerObjects(
        int|Partner|PartnerWork $partner,
        $model = Partner::class
    ): array|Collection {
        if (is_int($partner)) {
            $partner = $model::find($partner);
        }
        return $partner?->objects()->get() ?? collect([]);
    }

    public static function getPartnerForSelect(): array|Collection
    {
        return Partner::query()
            ->selectRaw('id, if(short_name IS NOT NULL, short_name, name) as pname')
            ->orderBy('pname', 'asc')
            ->pluck('pname', 'id');
    }

    public static function searchPartnerForSelect(string $query): array|Collection
    {
        return Partner::where('name', 'LIKE', "%{$query}%")
            ->orWhere('short_name', 'LIKE', "%{$query}%")
            ->orWhere('vat_id', 'LIKE', "%{$query}%")
            ->limit(50)
            ->selectRaw('id, if(short_name IS NOT NULL, short_name, name) as pname')
            ->pluck('pname', 'id')
            ->toArray();
    }

    public static function checkIfNipExists(string $nip): bool
    {
        return Partner::where('vat_id', $nip)
            ->exists();
    }
}
