<?php

namespace Database\Seeders;

use App\Models\SystemSetting;
use App\Models\Tenant;
use App\Repositories\SystemSettingsRepository;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SystemSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $repository = new SystemSettingsRepository();

        // Create some example system settings
        $repository->set(
            'app_name',
            'NativeT System',
            'Application name displayed in the system'
        );

        $repository->set(
            'maintenance_mode',
            false,
            'Whether the system is in maintenance mode'
        );

        $repository->set(
            'max_file_upload_size',
            10485760, // 10MB in bytes
            'Maximum file upload size in bytes'
        );

        $repository->set(
            'supported_languages',
            ['pl', 'en'],
            'List of supported languages in the system'
        );

        $repository->set(
            'email_settings',
            [
                'from_name' => 'NativeT System',
                'from_email' => '<EMAIL>',
                'reply_to' => '<EMAIL>',
            ],
            'Default email configuration settings'
        );

        // Set system tenant if there's an active tenant available
        $firstActiveTenant = Tenant::active()->first();
        if ($firstActiveTenant) {
            $repository->setSystemTenantId($firstActiveTenant->id);
            $this->command->info("Set system tenant to: {$firstActiveTenant->name}");
        }

        $this->command->info('System settings seeded successfully!');
    }
}
