<?php

namespace Database\Factories;

use App\Enums\SubscriptionStatus;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Subscription>
 */
class SubscriptionFactory extends Factory
{
    protected $model = Subscription::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $startsAt = fake()->dateTimeBetween('-1 month', 'now');
        $plan = Plan::factory()->create();

        return [
            'user_id' => User::factory(),
            'tenant_id' => Tenant::factory(),
            'plan_id' => $plan->id,
            'status' => fake()->randomElement(SubscriptionStatus::cases()),
            'price' => $plan->price,
            'starts_at' => $startsAt,
            'trial_ends_at' => null,
            'ends_at' => (clone $startsAt)->modify('+' . $plan->period->value . ' months'),
        ];
    }

    /**
     * Create subscription for specific user and tenant
     */
    public function forUserAndTenant(User $user, Tenant $tenant): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
            'tenant_id' => $tenant->id,
        ]);
    }

    /**
     * Create subscription with specific plan
     */
    public function withPlan(Plan $plan): static
    {
        $startsAt = fake()->dateTimeBetween('-1 month', 'now');

        return $this->state(fn (array $attributes) => [
            'plan_id' => $plan->id,
            'price' => $plan->price,
            'starts_at' => $startsAt,
            'ends_at' => (clone $startsAt)->modify('+' . $plan->period->value . ' months'),
        ]);
    }

    /**
     * Create active subscription
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => SubscriptionStatus::ACTIVE,
        ]);
    }

    /**
     * Create new subscription
     */
    public function newSub(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => SubscriptionStatus::NEW,
        ]);
    }

    /**
     * Create canceled subscription
     */
    public function canceled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => SubscriptionStatus::CANCELED,
        ]);
    }

    /**
     * Create expired subscription
     */
    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => SubscriptionStatus::EXPIRED,
            'ends_at' => fake()->dateTimeBetween('-1 month', '-1 day'),
        ]);
    }

    /**
     * Create trial subscription
     */
    public function trial(): static
    {
        $startsAt = fake()->dateTimeBetween('-1 month', 'now');
        $trialEndsAt = (clone $startsAt)->modify('+14 days');

        return $this->state(fn (array $attributes) => [
            'status' => SubscriptionStatus::TRIAL,
            'trial_ends_at' => $trialEndsAt,
        ]);
    }

    /**
     * Create subscription starting today
     */
    public function startingToday(): static
    {
        return $this->state(function (array $attributes) {
            $plan = Plan::find($attributes['plan_id']) ?? Plan::factory()->create();

            return [
                'starts_at' => now(),
                'ends_at' => now()->addMonths($plan->period->value)->endOfDay(),
            ];
        });
    }
}
