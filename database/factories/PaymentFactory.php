<?php

namespace Database\Factories;

use App\Enums\PaymentStatus;
use App\Models\Payment;
use App\Models\Subscription;
use App\Models\Tenant;
use App\Models\User;
use App\Services\Payments\PayUPaymentProvider;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Payment>
 */
class PaymentFactory extends Factory
{
    protected $model = Payment::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'tenant_id' => Tenant::factory(),
            'subscription_id' => Subscription::factory(),
            'hash' => (string) Str::ulid(),
            'provider' => PayUPaymentProvider::class,
            'provider_payment_id' => $this->faker->uuid(),
            'amount' => $this->faker->numberBetween(1000, 50000), // Amount in cents
            'currency' => 'PLN',
            'status' => PaymentStatus::PENDING,
            'paid_at' => null,
            'payment_method' => $this->faker->randomElement(['card', 'bank_transfer', 'blik']),
            'payment_transaction_id' => $this->faker->uuid(),
            'meta' => [],
        ];
    }

    /**
     * Indicate that the payment is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => PaymentStatus::PENDING,
            'paid_at' => null,
        ]);
    }

    /**
     * Indicate that the payment is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => PaymentStatus::COMPLETED,
            'paid_at' => $this->faker->dateTimeBetween('-1 hour', 'now'),
        ]);
    }

    /**
     * Indicate that the payment has failed.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => PaymentStatus::FAILED,
            'paid_at' => null,
        ]);
    }

    /**
     * Indicate that the payment was canceled.
     */
    public function canceled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => PaymentStatus::CANCELED,
            'paid_at' => null,
        ]);
    }

    /**
     * Indicate that the payment has an error.
     */
    public function error(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => PaymentStatus::ERROR,
            'paid_at' => null,
        ]);
    }

    /**
     * Create a payment for specific user and tenant.
     */
    public function forUserAndTenant(User $user, Tenant $tenant): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
            'tenant_id' => $tenant->id,
        ]);
    }

    /**
     * Create a payment with a specific subscription.
     */
    public function withSubscription(Subscription $subscription): static
    {
        return $this->state(fn (array $attributes) => [
            'subscription_id' => $subscription->id,
            'user_id' => $subscription->user_id,
            'tenant_id' => $subscription->tenant_id,
        ]);
    }

    /**
     * Create a payment with a specific amount.
     */
    public function withAmount(int $amountInCents): static
    {
        return $this->state(fn (array $attributes) => [
            'amount' => $amountInCents,
        ]);
    }
}
