<?php

namespace Database\Factories;

use App\Enums\PlanPeriod;
use App\Enums\PlanType;
use App\Enums\SystemModules;
use App\Models\Plan;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Plan>
 */
class PlanFactory extends Factory
{
    protected $model = Plan::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $planNames = [
            'Plan próbny',
            'Podstawowy',
            'Standard',
            'Premium',
            'Enterprise'
        ];

        $descriptions = [
            'Plan próbny' => 'Bezpłatny plan próbny z podstawowymi funkcjami',
            'Podstawowy' => 'Plan podstawowy dla małych firm',
            'Standard' => 'Plan standardowy z rozszerzonymi funkcjami',
            'Premium' => 'Plan premium dla średnich firm',
            'Enterprise' => 'Plan enterprise dla dużych organizacji'
        ];

        $name = fake()->randomElement($planNames);
        
        return [
            'name' => $name,
            'description' => $descriptions[$name] ?? 'Opis planu subskrypcji',
            'price' => fake()->randomFloat(2, 0, 500),
            'period' => fake()->randomElement(PlanPeriod::cases()),
            'type' => fake()->randomElement(PlanType::cases()),
            'features' => $this->generateFeatures(),
            'is_active' => true,
        ];
    }

    /**
     * Generate realistic features array
     */
    private function generateFeatures(): array
    {
        $allFeatures = [
            SystemModules::WAREHOUSE->value,
            SystemModules::INVOICES->value,
            SystemModules::SIMPLE_PRODUCTS->value,
            SystemModules::PURCHASE_INVOICES->value,
            SystemModules::JOB_TASKS->value,
            SystemModules::SIMPLE_CHARTS->value,
            SystemModules::EMPLOYEES->value,
            SystemModules::LOGO->value,
            SystemModules::INVOICE_TEMPLATES->value,
        ];

        // Always include INVOICES as it's basic
        $features = [SystemModules::INVOICES->value];
        
        // Add random additional features
        $additionalFeatures = fake()->randomElements(
            array_diff($allFeatures, $features),
            fake()->numberBetween(1, 5)
        );

        return array_merge($features, $additionalFeatures);
    }

    /**
     * Create a trial plan
     */
    public function trial(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Plan próbny',
            'description' => 'Bezpłatny plan próbny z podstawowymi funkcjami',
            'price' => 0,
            'period' => PlanPeriod::MONTH,
            'type' => PlanType::TRIAL,
            'features' => [
                SystemModules::INVOICES->value,
                SystemModules::SIMPLE_PRODUCTS->value,
            ],
        ]);
    }

    /**
     * Create a basic paid plan
     */
    public function basic(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Podstawowy',
            'description' => 'Plan podstawowy dla małych firm',
            'price' => 19.00,
            'period' => PlanPeriod::MONTH,
            'type' => PlanType::PAID,
            'features' => [
                SystemModules::INVOICES->value,
                SystemModules::SIMPLE_PRODUCTS->value,
            ],
        ]);
    }

    /**
     * Create a standard plan
     */
    public function standard(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Standard',
            'description' => 'Plan standardowy z rozszerzonymi funkcjami',
            'price' => 390.00,
            'period' => PlanPeriod::YEAR,
            'type' => PlanType::PAID,
            'features' => [
                SystemModules::INVOICES->value,
                SystemModules::SIMPLE_PRODUCTS->value,
                SystemModules::PURCHASE_INVOICES->value,
                SystemModules::SIMPLE_CHARTS->value,
                SystemModules::LOGO->value,
                SystemModules::INVOICE_TEMPLATES->value,
            ],
        ]);
    }

    /**
     * Create an inactive plan
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create an active plan
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }
}
