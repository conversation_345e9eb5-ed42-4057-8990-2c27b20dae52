<?php

namespace Database\Factories;

use App\Models\SystemSetting;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SystemSetting>
 */
class SystemSettingFactory extends Factory
{
    protected $model = SystemSetting::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'key' => $this->faker->unique()->slug(2),
            'value' => $this->faker->randomElement([
                $this->faker->word(),
                $this->faker->numberBetween(1, 100),
                $this->faker->boolean(),
                ['array' => $this->faker->words(3)],
            ]),
            'description' => $this->faker->sentence(),
        ];
    }

    /**
     * Create a system tenant setting
     */
    public function systemTenant(int $tenantId): static
    {
        return $this->state([
            'key' => 'system_tenant_id',
            'value' => $tenantId,
            'description' => 'ID of the system tenant used for system-wide operations',
        ]);
    }

    /**
     * Create a setting with specific key and value
     */
    public function withKeyValue(string $key, $value, ?string $description = null): static
    {
        return $this->state([
            'key' => $key,
            'value' => $value,
            'description' => $description,
        ]);
    }
}
