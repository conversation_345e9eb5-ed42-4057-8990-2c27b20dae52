# System Settings Feature

## Overview

The System Settings feature provides a comprehensive configuration management system for the Laravel application. It allows administrators to manage system-wide settings with caching support, a Filament admin interface, and easy programmatic access.

## Components

### 1. Database Structure

**Table: `system_settings`**
- `id` - Primary key
- `key` - Unique setting identifier
- `value` - JSON-encoded setting value
- `description` - Human-readable description
- `created_at` / `updated_at` - Timestamps

### 2. Model: `App\Models\SystemSetting`

Basic Eloquent model with JSON casting for the value field and helper methods:
- `getValue(string $key, $default = null)` - Static method to get a setting
- `setValue(string $key, $value, ?string $description = null)` - Static method to set a setting
- `removeSetting(string $key)` - Static method to remove a setting
- `exists(string $key)` - Static method to check if setting exists

### 3. Repository: `App\Repositories\SystemSettingsRepository`

Cache-first repository with the following methods:
- `get(string $key, $default = null)` - Get setting with caching
- `set(string $key, $value, ?string $description = null)` - Set setting and update cache
- `forget(string $key)` - Remove setting from DB and cache
- `all()` - Get all settings with caching
- `exists(string $key)` - Check if setting exists
- `getSystemTenantId()` - Get system tenant ID
- `setSystemTenantId(int $tenantId)` - Set system tenant ID
- `getSystemBankAccount()` - Get system bank account identifier
- `setSystemBankAccount(string $bankAccountIdentifier)` - Set system bank account
- `getSystemFvsDocumentSeries()` - Get system FVS document series ID
- `setSystemFvsDocumentSeries(int $seriesId)` - Set system FVS document series
- `getSystemTenantAdminUser()` - Get system tenant admin user ID
- `setSystemTenantAdminUser(int $userId)` - Set system tenant admin user
- `clearCache()` - Clear all settings cache

**Caching Strategy:**
- 24-hour TTL for all cached settings
- Cache keys prefixed with `system_settings.`
- Automatic cache invalidation on updates
- Cache-first approach for reads

### 4. Filament Admin Page: `app/Filament/Pages/SystemSettingsPage`

Admin interface accessible only to super admins (`isGod()` users) with:
- **System Tenant Configuration**: Select default tenant for system operations
- **Bank Account Selection**: Choose from tenant's available bank accounts
- **Document Series Selection**: Select FVS (Sales Invoice) document series
- **Tenant Admin User Selection**: Choose tenant admin user for system operations
- Tenant information display with reactive updates
- Save and clear cache actions
- Comprehensive form validation and error handling

**Access Control:**
- Only super admins can access the page
- Located in admin panel at `/adm/system-settings`
- Navigation group: "System"

**Features:**
- Reactive form fields that update based on tenant selection
- Validation ensures selected values belong to chosen tenant
- Handles cases where tenant has no configured data
- Live updates when tenant selection changes

### 5. Helper Functions

Global helper functions for easy access:
- `systemSetting(string $key, $default = null)` - Get a system setting
- `setSystemSetting(string $key, $value, ?string $description = null)` - Set a system setting
- `systemTenant()` - Get the system tenant model
- `systemBankAccount()` - Get the system bank account as DTOBankData
- `systemFvsDocumentSeries()` - Get the system FVS document series pattern
- `systemTenantAdminUser()` - Get the system tenant admin user with profile

### 6. Artisan Command: `system:settings`

Command-line interface for managing settings:

```bash
# List all settings
./server artisan system:settings list

# Get a specific setting
./server artisan system:settings get app_name

# Set a setting
./server artisan system:settings set app_name "My App" --description="Application name"

# Clear cache
./server artisan system:settings clear-cache
```

## Usage Examples

### Basic Usage

```php
// Get a setting
$appName = systemSetting('app_name', 'Default App Name');

// Set a setting
setSystemSetting('maintenance_mode', true, 'Enable maintenance mode');

// Get system tenant
$tenant = systemTenant();

// Get system bank account
$bankAccount = systemBankAccount();
if ($bankAccount) {
    echo "Bank: {$bankAccount->bank_name}, Account: {$bankAccount->bank_account}";
}

// Get system FVS document series
$fvsSeries = systemFvsDocumentSeries();
if ($fvsSeries) {
    echo "FVS Series: {$fvsSeries->name} ({$fvsSeries->pattern})";
}

// Get system tenant admin user
$adminUser = systemTenantAdminUser();
if ($adminUser) {
    echo "Admin: {$adminUser->profile->fullName()} ({$adminUser->email})";
}
```

### Repository Usage

```php
use App\Repositories\SystemSettingsRepository;

$repository = new SystemSettingsRepository();

// Complex settings
$repository->set('email_config', [
    'smtp_host' => 'smtp.example.com',
    'smtp_port' => 587,
    'encryption' => 'tls'
], 'Email server configuration');

// Get with default
$maxSize = $repository->get('max_upload_size', ********);
```

### System Tenant Configuration

```php
// Set system tenant
$repository = new SystemSettingsRepository();
$repository->setSystemTenantId(1);

// Get system tenant
$systemTenant = systemTenant();
if ($systemTenant) {
    echo "System tenant: " . $systemTenant->name;
}
```

## Testing

### Unit Tests

Run the comprehensive test suite:

```bash
./server artisan test tests/Unit/SystemSettingsRepositoryTest.php
```

Tests cover:
- Setting and getting values
- Default value handling
- System tenant management
- Cache functionality
- Setting deletion

### Factory and Seeding

```php
// Create test settings
SystemSetting::factory()->count(5)->create();

// Create specific setting
SystemSetting::factory()->withKeyValue('test_key', 'test_value')->create();

// Seed example settings
./server artisan db:seed --class=SystemSettingsSeeder
```

## Security Considerations

1. **Access Control**: Only super admins can access the Filament page
2. **Input Validation**: All inputs are validated through Filament forms
3. **Cache Security**: Cache keys are prefixed to avoid collisions
4. **JSON Safety**: Values are safely JSON encoded/decoded

## Performance

1. **Caching**: 24-hour cache TTL reduces database queries
2. **Efficient Queries**: Indexed key column for fast lookups
3. **Batch Operations**: Repository supports efficient bulk operations
4. **Memory Usage**: Static repository instances in helpers reduce object creation

## Migration and Setup

1. Run the migration:
```bash
./server artisan migrate
```

2. Seed example settings (optional):
```bash
./server artisan db:seed --class=SystemSettingsSeeder
```

3. Access admin interface at `/adm/system-settings` (super admin required)

## Integration with Existing Features

- **Tenant System**: Integrates with existing tenant model and relationships
- **Filament**: Follows existing Filament patterns and styling
- **Cache**: Uses Laravel's built-in caching system
- **Commands**: Follows existing Artisan command patterns
- **Testing**: Compatible with existing test infrastructure
