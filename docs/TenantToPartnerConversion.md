# Tenant to Partner Conversion

This document describes the `convertToPartner()` method in the `App\Models\Tenant` class that converts Tenant data to Partner model format.

## Overview

The conversion method creates a new `Partner` instance and maps compatible fields from the `Tenant` model. This is useful for migrating tenant data to partner records or creating partner representations of tenants.

## Usage

```php
$tenant = Tenant::find(1);
$partner = $tenant->convertToPartner();

// The partner is not automatically saved - you need to save it manually
$partner->save();
```

## Field Mappings

### Direct Mappings (Same Field Names and Types)

These fields are directly copied from Tenant to Partner:

| Tenant Field | Partner Field | Type | Description |
|--------------|---------------|------|-------------|
| `name` | `name` | string | Company/tenant name |
| `address` | `address` | text | Physical address |
| `postcode` | `postcode` | string(100) | Postal code |
| `city` | `city` | string(100) | City name |
| `phone` | `phone` | string(100) | Phone number |
| `email` | `email` | string(60) | Email address |
| `contact_name` | `contact_name` | string(255) | Contact person name |
| `website` | `website` | string(255) | Website URL |
| `vat_id` | `vat_id` | string(255) | VAT identification number |
| `vat_type` | `vat_type` | PartnerVATTypes | VAT type enum |
| `business_type` | `business_type` | PartnerBusinessTypes | Business type enum |
| `tax_residency_country` | `tax_residency_country` | TaxResidencyCountries | Tax residency country |
| `is_active` | `is_active` | boolean | Active status |

### Special Mappings

| Tenant Field | Partner Field | Mapping Logic |
|--------------|---------------|---------------|
| `hash` | `hash` | Copied directly (Partner can auto-generate if needed) |
| `id` | `installation` | Tenant's ID becomes Partner's installation field |

### Partner-Only Fields (Set to Defaults)

These fields exist in Partner but not in Tenant, so they are set to null or default values:

| Partner Field | Default Value | Description |
|---------------|---------------|-------------|
| `short_name` | `null` | Short name for the partner |
| `country_id` | `null` | Country identifier |
| `bank_name` | `null` | Bank name |
| `bank_iban` | `null` | Bank IBAN |
| `bank_swift` | `null` | Bank SWIFT code |
| `bank_account` | `null` | Bank account number |

### Unmappable Fields (Tenant-Specific)

These Tenant fields cannot be mapped to Partner because Partner doesn't have equivalent fields:

| Tenant Field | Type | Reason Not Mappable |
|--------------|------|---------------------|
| `system_domain` | string | Partner doesn't have system domain concept |
| `tax_type` | TaxTypePL | Partner doesn't have tax type field |
| `accounting_type` | AccountingTypesPL | Partner doesn't have accounting type field |
| `config` | array/JSON | Tenant-specific configuration data |
| Subscription fields | various | Tenant-specific subscription management |

## Enum Compatibility

The following enums are shared between Tenant and Partner models:

- **PartnerVATTypes**: `LOCAL`, `EU`, `NONEU`, `EU3P`, `OSSRoutine`, `NOTVAT`
- **PartnerBusinessTypes**: `INDIVIDUAL`, `BUSINESS`
- **TaxResidencyCountries**: All EU country codes (PL, DE, FR, etc.)

## Example

```php
// Create a tenant with sample data
$tenant = new Tenant([
    'id' => 123,
    'hash' => 'sample_hash_******************************1234',
    'name' => 'Sample Company Ltd.',
    'address' => '123 Business Street',
    'postcode' => '12-345',
    'city' => 'Warsaw',
    'phone' => '+48 ***********',
    'email' => '<EMAIL>',
    'contact_name' => 'John Smith',
    'website' => 'https://sample.com',
    'vat_id' => '**********',
    'vat_type' => PartnerVATTypes::LOCAL,
    'business_type' => PartnerBusinessTypes::BUSINESS,
    'tax_residency_country' => TaxResidencyCountries::PL,
    'is_active' => true,
    // These fields won't be mapped:
    'system_domain' => 'sample.system.com',
    'tax_type' => TaxTypePL::PROGRESSIVE,
    'accounting_type' => AccountingTypesPL::IOR,
    'config' => ['modules' => ['invoices']],
]);

// Convert to partner
$partner = $tenant->convertToPartner();

// Save the partner
$partner->save();
```

## Testing

The conversion method is thoroughly tested in `tests/Unit/TenantToPartnerConversionTest.php` with the following test cases:

1. **Full data conversion**: Tests all mappable fields with complete data
2. **Minimal data conversion**: Tests with minimal required fields
3. **Enum value conversion**: Tests different enum combinations
4. **Unmappable fields documentation**: Verifies unmappable fields are properly handled

## Notes

- The converted Partner instance is **not automatically saved** - you must call `save()` manually
- The Partner model will auto-generate a hash if none is provided, but the conversion copies the Tenant's hash
- Bank account information fields are set to null and can be populated separately if needed
- The `installation` field in Partner references the original Tenant's ID, maintaining the relationship
